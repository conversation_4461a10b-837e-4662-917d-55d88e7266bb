# Implementation Plan

- [x] 1. Create compact layout foundation and styling utilities
  - Create CSS utility classes for compact spacing and typography
  - Define color scheme constants for price category backgrounds
  - Implement responsive grid system for the new layout
  - _Requirements: 1.1, 2.1, 2.2, 3.3_

- [-] 2. Implement price field grouping and visual organization
  - [x] 2.1 Create PriceFieldGroup component for category organization
    - Build reusable component for price field groups with icons and backgrounds
    - Implement visual grouping with subtle borders and background colors
    - Add category icons (Type, Volume2, Image, Brain) from Lucide React
    - _Requirements: 1.2, 3.1, 3.2_

  - [ ] 2.2 Create compact form field components
    - Build CompactFormField component with smaller fonts and reduced spacing
    - Implement compact input styling with proper placeholder formatting
    - Create compact label and description components
    - _Requirements: 2.1, 2.2, 2.3_

- [ ] 3. Refactor edit modal layout structure
  - [ ] 3.1 Remove tab-based navigation system
    - Remove Tabs, TabsList, TabsTrigger, and TabsContent components
    - Restructure form layout to display all fields simultaneously
    - Update modal content structure for single-page layout
    - _Requirements: 1.1, 1.3_

  - [ ] 3.2 Implement new grid-based layout
    - Create 3-column grid for main price categories (text, audio, image)
    - Implement single-column section for reasoning prices
    - Add responsive breakpoints for tablet and mobile layouts
    - _Requirements: 1.4, 5.1, 5.2_

- [ ] 4. Update typography and spacing throughout modal
  - [ ] 4.1 Apply compact font sizes to all text elements
    - Update modal title to use text-lg instead of default
    - Change descriptions to text-xs throughout the component
    - Apply text-xs to form labels and text-sm to form values
    - _Requirements: 2.1, 2.4_

  - [ ] 4.2 Reduce spacing and padding across components
    - Update header padding to pb-3 from default
    - Change form spacing from space-y-6 to space-y-3
    - Reduce gap between grid elements from gap-4 to gap-3
    - _Requirements: 2.2, 2.3_

- [ ] 5. Implement visual hierarchy improvements
  - [ ] 5.1 Add category-specific background colors and styling
    - Apply bg-blue-50/30 for text price section
    - Apply bg-green-50/30 for audio price section
    - Apply bg-purple-50/30 for image price section
    - Apply bg-orange-50/30 for reasoning price section
    - _Requirements: 3.1, 3.4_

  - [ ] 5.2 Enhance visual separation between sections
    - Add subtle borders (border-gray-100) to price group containers
    - Implement rounded corners (rounded-md) for modern appearance
    - Add proper spacing between different price categories
    - _Requirements: 3.1, 3.3_

- [ ] 6. Ensure responsive design and mobile compatibility
  - [ ] 6.1 Implement responsive grid breakpoints
    - Configure 3-column layout for desktop (lg+)
    - Set up 2-column layout for tablet (md)
    - Create single-column stack for mobile (sm and below)
    - _Requirements: 5.1, 5.2_

  - [ ] 6.2 Optimize touch interactions for mobile devices
    - Ensure input fields have appropriate touch target sizes
    - Test and adjust spacing for mobile usability
    - Verify modal scrolling behavior on mobile devices
    - _Requirements: 5.3, 5.4_

- [ ] 7. Maintain existing functionality and validation
  - [ ] 7.1 Preserve all form validation rules and error handling
    - Ensure all existing validation schemas continue to work
    - Update error message styling to use compact text-xs format
    - Test form submission and API integration
    - _Requirements: 4.1, 4.2_

  - [ ] 7.2 Update loading states for new layout
    - Adjust skeleton loading components for compact layout
    - Update skeleton heights to match new spacing
    - Test loading state appearance across different screen sizes
    - _Requirements: 4.3_

- [ ] 8. Add accessibility improvements and testing
  - [ ] 8.1 Implement proper ARIA labels and semantic structure
    - Add aria-labels for price category sections
    - Ensure proper heading hierarchy is maintained
    - Verify keyboard navigation order through form fields
    - _Requirements: 4.4_

  - [ ] 8.2 Test color contrast and visual accessibility
    - Validate color contrast ratios for new background colors
    - Test readability with smaller font sizes
    - Ensure sufficient visual distinction between categories
    - _Requirements: 3.2, 3.4_

- [ ] 9. Create comprehensive test coverage
  - [ ] 9.1 Write unit tests for new components
    - Test PriceFieldGroup component rendering and props
    - Test CompactFormField component functionality
    - Verify responsive layout behavior
    - _Requirements: 1.1, 1.2, 1.3, 1.4_

  - [ ] 9.2 Add integration tests for modal functionality
    - Test complete form submission workflow
    - Verify data loading and error handling
    - Test modal open/close behavior with new layout
    - _Requirements: 4.1, 4.2, 4.3_