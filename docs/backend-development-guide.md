# Backend Development Guide

## Tech Stack
- **Core**: Spring Boot 3.5.3 + Kotlin 1.9.25 + Java 21
- **Reactive**: WebFlux + Reactor + Kotlin Coroutines
- **DB**: PostgreSQL + JPA + Flyway
- **Build**: Gradle Kotlin DSL

## Architecture (DDD)
```
io.cliveyou.claudecodeproxybackend/
├── common/           # Shared (exceptions, request/response models)
├── config/           # Spring configs
├── keymanagement/    # Main module
│   ├── application/  # Business logic (services, handlers, timers)
│   ├── domain/       # Domain models & interfaces
│   ├── facade/       # Controllers & DTOs
│   └── infrastructure/ # DB entities, repos, external clients
```

## API Pattern
```kotlin
// Controller: Returns Mono<PlatformResult<T>>
@RestController
@RequestMapping("/api/v1/resource")
class ResourceController(private val service: ResourceService) {
    @PostMapping
    fun create(@RequestBody req: CreateRequest): Mono<PlatformResult<ResourceDto>> {
        return Mono.fromCallable {
            runBlocking { PlatformResult.success(service.create(req)) }
        }
    }
}

// Service: Uses suspend functions
@Service
@Transactional
class ResourceService(private val repo: ResourceRepository) {
    suspend fun create(req: CreateRequest): ResourceDto {
        // validate -> save -> convert to DTO
    }
}

// Response wrapper
data class PlatformResult<T>(
    val requestId: Long = System.currentTimeMillis(),
    val code: String,
    val desc: String,
    val data: T? = null,
    val success: Boolean
)
```

## Entity Pattern
```kotlin
@Entity
@Table(name = "table_name")
data class EntityName(
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long? = null,
    val name: String,
    @Enumerated(EnumType.STRING) val status: StatusEnum,
    val createdAt: LocalDateTime = LocalDateTime.now()
)
```

## Repository
```kotlin
interface ResourceRepository : 
    JpaRepository<Resource, Long>,
    JpaSpecificationExecutor<Resource>
```

## Exception Handling
```kotlin
// Custom exceptions extend BusinessException
class ResourceNotFoundException(msg: String) : BusinessException(msg, "404")

// Global handler returns PlatformResult
@ControllerAdvice
class GlobalWebFluxExceptionHandler {
    @ExceptionHandler(BusinessException::class)
    fun handle(ex: BusinessException) = Mono.just(
        PlatformResult.error(ex.errorCode, ex.message)
    )
}
```

## Database Migration
```sql
-- src/main/resources/db/migration/V{version}__{description}.sql
CREATE TABLE table_name (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL
);
```

## Scheduled Tasks
```kotlin
@Component
class TokenRefreshTimer(private val service: CommonTokenKeyService) {
    @Scheduled(fixedRate = 600000) // 10min
    fun refreshTokens() = runBlocking {
        service.refreshExpiredTokens()
    }
}
```

## Key Patterns
- **Controllers**: Return `Mono<PlatformResult<T>>`
- **Services**: Use `suspend` functions with `@Transactional`
- **Entities**: Use `data class` for immutability
- **Repos**: Extend `JpaRepository` + `JpaSpecificationExecutor`
- **Errors**: Custom exceptions → Global handler → PlatformResult
- **Config**: Use `application.yml` for settings
- **Testing**: JUnit 5 + MockK + `runBlocking` for coroutines

## API Endpoints
- Base: `/api/v1/`
- CRUD: POST `/`, GET `/{id}`, PUT `/{id}`, DELETE `/{id}`
- Pagination: POST `/page` with PageRequest body

## Development Flow
1. Create Flyway migration → 2. Define domain model → 3. Create JPA entity
4. Implement repository → 5. Write service logic → 6. Create DTOs
7. Implement controller → 8. Write tests

## Quick Reference
- **DB**: PostgreSQL @ localhost:5432/key_management (user: postgres, pw: root)
- **Port**: 8080
- **Logs**: Use `KotlinLogging.logger {}`
- **Never**: Log sensitive data, use blocking I/O in reactive context
- **Always**: Validate inputs, handle exceptions, use configuration props