# Frontend Development Guide

## Tech Stack
- **Core**: React 19 + TypeScript + Vite
- **Router**: React Router v7
- **Styling**: Tailwind CSS v4 + shadcn/ui
- **State**: Jo<PERSON>
- **Forms**: React Hook Form + Zod
- **HTTP**: Axios + ahooks
- **Modals**: PushModal
- **Icons**: Lucide React
- **Notifications**: Sonner

## Project Structure
```
src/
├── api/{module}/          # API methods & TypeScript models
├── app/dash/{page}/       # Dashboard pages (page.tsx + column.tsx)
├── components/            # Reusable components (ui/, table/, modals/, layout/)
├── hooks/                 # Custom hooks
├── lib/                   # Utilities
├── router/               # Route config
└── state/                # Global state (Jotai)
```

## Naming Conventions
- **Files**: kebab-case.tsx (key-management.tsx)
- **Components**: PascalCase (KeyManagementPage)
- **Functions**: camelCase (handleCreateSuccess)
- **Constants**: UPPER_SNAKE_CASE (API_BASE_URL)

## API Pattern
```typescript
// api/{module}/{module}-api.ts
export const moduleApi = {
    pageItems: async (params: FetchParams): Promise<FetchResponse<ModelType>> => {
        return await apiClient.get<FetchResponse<ModelType>>({
            url: "/api/private/module/page",
            params: { ...params.searchParams, pageSize: params.pagination.pageSize }
        });
    },
    createItem: async (request: CreateRequest): Promise<ModelType> => 
        apiClient.post<ModelType>({ url: "/api/private/module/create", data: request }),
    updateItem: async (request: UpdateRequest): Promise<ModelType> => 
        apiClient.put<ModelType>({ url: "/api/private/module/update", data: request }),
    deleteItem: async (id: number): Promise<void> => 
        apiClient.delete<void>({ url: `/api/private/module/delete/${id}` })
};

// Model types
export interface ModelType {
    id: number;
    name: string;
    status: StatusEnum;
    createdAt: string;
}
```

## Page Component Template
```typescript
// app/dash/{module}/page.tsx
export default function ModulePage() {
    const setRefreshTable = useSetAtom(refreshTableAtom);
    const { getParam, addParam } = useSearchParamsManager();
    
    // Chinese IME handling
    const [isComposing, setIsComposing] = useState(false);
    const [inputValue, setInputValue] = useState(getParam("name") || "");

    return (
        <div className="mx-auto bg-white rounded-lg shadow-borders-base h-full shadow-sm border border-zinc-200 py-2">
            <div className="flex flex-col gap-2 h-full">
                {/* Header */}
                <header className="px-4 py-2 space-y-1 flex flex-row items-center justify-between">
                    <div>
                        <h1 className="text-[18px]">页面标题</h1>
                        <h2 className="text-[13px] text-zinc-500">页面描述</h2>
                    </div>
                    <div className="pr-4">
                        <CommonButton onClick={() => pushModal("CreateModal", {
                            onSuccess: () => setRefreshTable(prev => prev + 1)
                        })}>
                            <PlusIcon className="w-4 h-4" />新建
                        </CommonButton>
                    </div>
                </header>
                
                {/* Table */}
                <DataTable
                    columns={moduleColumn}
                    isNeedSelect={true}
                    onFetch={moduleApi.pageItems}
                    toolbar={(table, tableId) => (
                        <DataTableToolbar table={table} tableId={tableId}>
                            <ToolbarLeft>
                                {/* Search with Chinese IME support */}
                                <ToolbarItem>
                                    <Input
                                        placeholder="搜索名称"
                                        value={inputValue}
                                        onChange={(e) => {
                                            setInputValue(e.target.value);
                                            if (!isComposing) addParam("name", e.target.value);
                                        }}
                                        onCompositionStart={() => setIsComposing(true)}
                                        onCompositionEnd={(e) => {
                                            setIsComposing(false);
                                            addParam("name", e.currentTarget.value);
                                        }}
                                    />
                                </ToolbarItem>
                            </ToolbarLeft>
                        </DataTableToolbar>
                    )}
                />
            </div>
        </div>
    );
}
```

## Table Column Template
```typescript
// app/dash/{module}/{module}-column.tsx
export const moduleColumn: ColumnDef<ModelType>[] = [
    {
        id: "name",
        header: "名称",
        cell: ({ row }) => <div className="font-medium">{row.original.name}</div>
    },
    {
        id: "status",
        header: "状态",
        cell: ({ row }) => {
            const config = getStatusConfig(row.original.status);
            return (
                <Badge variant="outline" className="gap-1.5 text-zinc-600">
                    <span className={cn("size-1.5 rounded-full", config.color)} />
                    {config.text}
                </Badge>
            );
        }
    },
    {
        id: "actions",
        header: "操作",
        cell: ({ row }) => {
            const setRefreshTable = useSetAtom(refreshTableAtom);
            return (
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuItem onClick={() => pushModal("EditModal", { itemData: row.original })}>
                            <Edit className="mr-2 h-4 w-4" />编辑
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDelete(row.original)} className="text-red-600">
                            <Trash2 className="mr-2 h-4 w-4" />删除
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            );
        }
    }
];
```

## Modal Template
```typescript
// components/modals/{module}/create-modal.tsx
const formSchema = z.object({
    name: z.string().min(1, "名称不能为空"),
    status: z.nativeEnum(StatusEnum)
});

export const CreateModal = ({ onClose, onSuccess }: CreateModalProps) => {
    const [open, setOpen] = useState(true);
    const form = useForm<FormData>({
        resolver: zodResolver(formSchema),
        defaultValues: { name: "", status: StatusEnum.ACTIVE }
    });

    const { run: createItem, loading } = useRequest(moduleApi.createItem, {
        manual: true,
        onSuccess: () => {
            toast.success("创建成功");
            onSuccess?.();
            setOpen(false);
        },
        onError: (error) => toast.error(`创建失败: ${error.message}`)
    });

    return (
        <Dialog open={open} onOpenChange={() => { setOpen(false); onClose(); }}>
            <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                    <DialogTitle>新建项目</DialogTitle>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(createItem)} className="space-y-4">
                        <FormField
                            control={form.control}
                            name="name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>名称 *</FormLabel>
                                    <FormControl>
                                        <Input placeholder="请输入名称" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <DialogFooter>
                            <CommonButton variant="outline" onClick={() => setOpen(false)}>取消</CommonButton>
                            <CommonButton type="submit" loading={loading}>创建</CommonButton>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
};
```

## Key Patterns & Utilities
```typescript
// URL params management
const { getParam, addParam } = useSearchParamsManager();
addParam("status", "ACTIVE");

// Table refresh
const setRefreshTable = useSetAtom(refreshTableAtom);
setRefreshTable(prev => prev + 1);

// Modal calls (no isOpen prop needed)
pushModal("CreateModal", { onSuccess: handleSuccess });

// Form validation
const schema = z.object({
    field: z.string().min(1, "字段不能为空"),
    email: z.string().email("邮箱格式错误").optional().or(z.literal(""))
});

// API error handling
const { run, loading } = useRequest(api.method, {
    manual: true,
    onSuccess: () => toast.success("操作成功"),
    onError: (error) => toast.error(`操作失败: ${error.message}`)
});
```

## Router & Menu Setup
```typescript
// router/self-route.tsx
{ path: "module", Component: ModulePage }

// components/layout/app-sidebar.tsx
{
    title: "模块管理",
    url: "/dashboard/module", 
    icon: ModuleIcon
}

// components/modals/index.ts
export const { pushModal } = createPushModal({
    modals: {
        "CreateModal": { Component: CreateModal, Wrapper: Wrapper }
    }
});
```

## Important Notes
- **Chinese IME**: Always handle `onCompositionStart/End` for search inputs
- **No enums as types**: Use `z.nativeEnum()` for Zod, avoid TS enum types (erasableSyntaxOnly rule)
- **Modal management**: PushModal handles open/close state automatically
- **File naming**: kebab-case for files, PascalCase for components
- **Table refresh**: Use `refreshTableAtom` for consistent table updates
- **Error handling**: Always use `toast` for user feedback
- **Import order**: React → 3rd party → UI → Business → API → Utils/State
-  后端 long type 对应的 react项目的类型是String 不能用number接收，会有精度问题

## Development Flow
1. Create API client & models → 2. Build page component → 3. Define table columns
4. Create modal forms → 5. Register modals → 6. Add routes & menu items