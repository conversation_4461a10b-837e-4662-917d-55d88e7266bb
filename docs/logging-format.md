# 日志格式规范

## 日志格式定义

日志格式采用结构化的方式，使用中文, 包含以下核心字段：

```
[OPERATION] [REASON] | [RESULT] | [SUGGESTION]
```

### 字段说明

- **OPERATION**: 操作类型，使用动词形式，如 Load Key、Remove Key、Add Key
- **REASON**: 操作原因，简述为什么执行此操作
- **RESULT**: 操作结果，成功/失败状态及关键信息
- **SUGGESTION**: 建议方案（可选），在失败或需要优化时提供

## 日志级别使用规范

- **INFO**: 正常操作流程
- **WARN**: 异常情况但不影响核心功能
- **ERROR**: 错误情况，需要关注处理

## 日志格式示例

### 成功操作
```kotlin
log.info { "Load Key | Request from channel: ${channel} | Success: Found key ${key.id}" }
log.info { "Add Key | Cooldown expired for ${key.id} | Success: Key restored to channel ${key.channel}" }
```

### 失败操作
```kotlin
log.error { "Load Key | No available keys for channel: ${channel} | Failed: Empty key list | Suggestion: Check key configuration or increase key pool" }
log.error { "Remove Key | Key ${key.id} not found in channel ${key.channel} | Failed: Key not exists | Suggestion: Verify key state before removal" }
```

### 警告操作
```kotlin
log.warn { "Load Key | High frequency requests for channel: ${channel} | Warning: ${requestCount} requests in last minute | Suggestion: Consider rate limiting" }
```

## 具体场景应用

### Key管理操作

#### 1. 加载Key
```kotlin
// 成功
log.info { "Load Key | Request from channel: ${channel} | Success: Found key ${key.id}" }

// 失败 - 无可用Key
log.error { "Load Key | No available keys for channel: ${channel} | Failed: Empty key list | Suggestion: Check key configuration or add more keys" }

// 失败 - 通道不存在
log.error { "Load Key | Channel ${channel} not found | Failed: Invalid channel | Suggestion: Verify channel configuration" }
```

#### 2. 添加Key
```kotlin
// 成功
log.info { "Add Key | New key registration for channel: ${channel} | Success: Key ${key.id} added" }

// 成功 - 冷却恢复
log.info { "Add Key | Cooldown expired for ${key.id} | Success: Key restored to channel ${key.channel}" }
```

#### 3. 移除Key
```kotlin
// 成功 - 冷却
log.info { "Remove Key | Rate limit exceeded for ${key.id} | Success: Key frozen for ${coldDownTime}" }

// 成功 - 手动移除
log.info { "Remove Key | Manual removal request for ${key.id} | Success: Key removed from channel ${key.channel}" }
```

#### 4. 冷却操作
```kotlin
// 开始冷却
log.info { "Freeze Key | Rate limit exceeded for ${key.id} | Success: Key frozen for ${coldDownTime}" }

// 冷却恢复
log.info { "Unfreeze Key | Cooldown period completed for ${key.id} | Success: Key restored to channel ${key.channel}" }

// 冷却失败
log.error { "Unfreeze Key | Cooldown recovery failed for ${key.id} | Failed: ${exception.message} | Suggestion: Check key state and retry manually" }
```

### 系统操作

#### 1. 服务初始化
```kotlin
log.info { "Initialize Service | System startup | Success: KeyManageService initialized with ${keyCount} keys" }
```

#### 2. 配置更新
```kotlin
log.info { "Update Config | Configuration file changed | Success: Reloaded ${keyCount} keys across ${channelCount} channels" }
```

### 性能监控

#### 1. 性能警告
```kotlin
log.warn { "Load Key | High frequency access for channel: ${channel} | Warning: ${requestCount} requests in 1 minute | Suggestion: Consider implementing request throttling" }
```

#### 2. 资源警告
```kotlin
log.warn { "Load Key | Low available keys for channel: ${channel} | Warning: Only ${availableCount} keys remaining | Suggestion: Add more keys or check key recovery status" }
```

## 实现建议

1. **使用常量定义操作类型**，避免字符串硬编码
2. **统一异常处理**，确保错误日志格式一致
3. **添加上下文信息**，如请求ID、用户ID等
4. **定期审查日志**，优化建议方案的实用性

## 日志解析工具

建议开发对应的日志解析工具，可以：
- 按操作类型筛选日志
- 统计成功/失败率
- 提取建议方案进行系统优化
- 生成操作报告和性能分析