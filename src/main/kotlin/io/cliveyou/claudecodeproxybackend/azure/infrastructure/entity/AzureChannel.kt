package io.cliveyou.claudecodeproxybackend.azure.infrastructure.entity

import jakarta.persistence.*
import java.time.LocalDateTime

/**
 * Azure Channel实体类
 * 对应Azure MySQL数据库中的channels表
 */
@Entity
@Table(name = "channels")
data class AzureChannel(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long? = null,

    /**
     * 类型
     */
    @Column(name = "type")
    val type: Long? = 0,

    /**
     * 密钥内容 - 存储API Key
     */
    @Column(name = "`key`", columnDefinition = "LONGTEXT")
    val key: String,

    /**
     * 基础URL
     */
    @Column(name = "base_url", columnDefinition = "LONGTEXT")
    val baseUrl: String? = null,

    /**
     * OpenAI组织
     */
    @Column(name = "open_ai_organization", columnDefinition = "LONGTEXT")
    val openAiOrganization: String? = null,

    /**
     * 状态 (1=启用, 0=禁用)
     */
    @Column(name = "status")
    val status: Long? = 1,

    /**
     * 名称
     */
    @Column(name = "name", length = 191)
    val name: String? = null,

    /**
     * 模型列表 JSON
     */
    @Column(name = "models", columnDefinition = "LONGTEXT")
    val models: String? = null,

    /**
     * 余额
     */
    @Column(name = "balance")
    val balance: Double? = null,

    /**
     * 已使用配额
     */
    @Column(name = "used_quota")
    val usedQuota: Long? = 0,



    /**
     * 分组
     */
    @Column(name = "`group`", length = 64)
    val group: String? = "default",

)
