package io.cliveyou.claudecodeproxybackend.common.exception

/**
 * 业务异常基类
 */
open class BusinessException(
    open val code: String = "BUSINESS_ERROR",
    override val message: String,
    override val cause: Throwable? = null
) : RuntimeException(message, cause)

/**
 * 参数无效异常
 */
class InvalidParameterException(
    message: String,
    cause: Throwable? = null
) : BusinessException("INVALID_PARAMETER", message, cause)

/**
 * 资源未找到异常
 */
class ResourceNotFoundException(
    message: String,
    cause: Throwable? = null
) : BusinessException("RESOURCE_NOT_FOUND", message, cause)

/**
 * 认证失败异常
 */
class AuthenticationException(
    message: String,
    cause: Throwable? = null
) : BusinessException("AUTHENTICATION_FAILED", message, cause)

/**
 * Token刷新异常
 */
class TokenRefreshException(
    message: String,
    cause: Throwable? = null
) : BusinessException("TOKEN_REFRESH_FAILED", message, cause)

/**
 * 外部服务调用异常
 */
class ExternalServiceException(
    message: String,
    cause: Throwable? = null
) : BusinessException("EXTERNAL_SERVICE_ERROR", message, cause)

/**
 * 密钥管理异常
 */
class KeyManagementException(
    message: String,
    cause: Throwable? = null
) : BusinessException("KEY_MANAGEMENT_ERROR", message, cause)