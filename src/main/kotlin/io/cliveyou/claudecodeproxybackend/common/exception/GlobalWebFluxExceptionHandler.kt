package io.cliveyou.claudecodeproxybackend.common.exception

import io.cliveyou.claudecodeproxybackend.common.response.PlatformResult
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.boot.autoconfigure.web.WebProperties
import org.springframework.boot.autoconfigure.web.reactive.error.AbstractErrorWebExceptionHandler
import org.springframework.boot.web.reactive.error.ErrorAttributes
import org.springframework.context.ApplicationContext
import org.springframework.core.annotation.Order
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.codec.ServerCodecConfigurer
import org.springframework.stereotype.Component
import org.springframework.web.reactive.function.BodyInserters
import org.springframework.web.reactive.function.server.*
import reactor.core.publisher.Mono

private val logger = KotlinLogging.logger {}

/**
 * WebFlux 全局异常处理器
 * 使用 @Order(-2) 确保在默认的异常处理器之前执行
 */
@Component
@Order(-2)
class GlobalWebFluxExceptionHandler(
    errorAttributes: ErrorAttributes,
    webProperties: WebProperties,
    applicationContext: ApplicationContext,
    serverCodecConfigurer: ServerCodecConfigurer
) : AbstractErrorWebExceptionHandler(
    errorAttributes,
    webProperties.resources,
    applicationContext
) {
    
    init {
        super.setMessageWriters(serverCodecConfigurer.writers)
        super.setMessageReaders(serverCodecConfigurer.readers)
    }

    override fun getRoutingFunction(errorAttributes: ErrorAttributes): RouterFunction<ServerResponse> {
        return RouterFunctions.route(RequestPredicates.all(), this::renderErrorResponse)
    }

    private fun renderErrorResponse(request: ServerRequest): Mono<ServerResponse> {
        val error = getError(request)
        
        logger.debug { "处理异常: ${error.javaClass.simpleName} - ${error.message}" }
        
        val result = when (error) {
            // 处理业务异常
            is BusinessException -> {
                logger.warn { "业务异常: ${error.code} - ${error.message}" }
                PlatformResult.error<Nothing>(error.code, error.message)
            }
            
            // 处理参数异常
            is IllegalArgumentException -> {
                logger.warn { "参数异常: ${error.message}" }
                PlatformResult.error<Nothing>("INVALID_ARGUMENT", error.message ?: "参数无效")
            }
            
            // 处理状态异常
            is IllegalStateException -> {
                logger.warn { "状态异常: ${error.message}" }
                PlatformResult.error<Nothing>("INVALID_STATE", error.message ?: "状态无效")
            }
            
            // 处理运行时异常
            is RuntimeException -> {
                val message = error.message
                if (message != null && (
                    message.contains("服务账户") || 
                    message.contains("Token刷新失败") ||
                    message.contains("刷新token失败") ||
                    message.contains("令牌") ||
                    message.contains("密钥")
                )) {
                    logger.warn { "运行时异常: $message" }
                    PlatformResult.error<Nothing>("RUNTIME_ERROR", message)
                } else {
                    logger.error(error) { "未处理的运行时异常" }
                    PlatformResult.error<Nothing>("INTERNAL_ERROR", "系统内部错误")
                }
            }
            
            // 处理其他异常
            else -> {
                logger.error(error) { "未处理的异常: ${error.javaClass.simpleName}" }
                PlatformResult.error<Nothing>("SYSTEM_ERROR", "系统异常，请稍后重试")
            }
        }
        
        return ServerResponse
            .status(HttpStatus.OK)
            .contentType(MediaType.APPLICATION_JSON)
            .body(BodyInserters.fromValue(result))
    }
}