package io.cliveyou.claudecodeproxybackend.common.response

/**
 * 统一响应结果类
 */
data class PlatformResult<T>(
    val code: String,
    val desc: String,
    val data: T? = null,
    val count: Long? = null,
    val success: Boolean,
) {
    companion object {

        /**
         * 成功响应
         */
        fun <T> success(data: T? = null, count: Long? = null): PlatformResult<T> {
            return PlatformResult(
                code = "200",
                desc = "操作成功",
                data = data,
                count = count,
                success = true
            )
        }

        /**
         * 成功响应（无数据）
         */
        fun success(): PlatformResult<Void> {
            return PlatformResult(
                code = "200",
                desc = "操作成功",
                data = null,
                count = null,
                success = true
            )
        }

        /**
         * 失败响应
         */
        fun <T> error(code: String = "500", desc: String = "操作失败"): PlatformResult<T> {
            return PlatformResult(
                code = code,
                desc = desc,
                data = null,
                count = null,
                success = false
            )
        }

        /**
         * 参数错误响应
         */
        fun <T> badRequest(desc: String = "参数错误"): PlatformResult<T> {
            return PlatformResult(
                code = "400",
                desc = desc,
                data = null,
                count = null,
                success = false
            )
        }

        /**
         * 未授权响应
         */
        fun <T> unauthorized(desc: String = "未授权"): PlatformResult<T> {
            return PlatformResult(
                code = "401",
                desc = desc,
                data = null,
                count = null,
                success = false
            )
        }

        /**
         * 禁止访问响应
         */
        fun <T> forbidden(desc: String = "禁止访问"): PlatformResult<T> {
            return PlatformResult(
                code = "403",
                desc = desc,
                data = null,
                count = null,
                success = false
            )
        }

        /**
         * 资源不存在响应
         */
        fun <T> notFound(desc: String = "资源不存在"): PlatformResult<T> {
            return PlatformResult(
                code = "404",
                desc = desc,
                data = null,
                count = null,
                success = false
            )
        }
    }
}
