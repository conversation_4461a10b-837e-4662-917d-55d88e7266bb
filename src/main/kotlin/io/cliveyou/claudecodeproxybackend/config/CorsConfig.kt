//package io.cliveyou.claudecodeproxybackend.config
//
//import org.springframework.context.annotation.Bean
//import org.springframework.context.annotation.Configuration
//import org.springframework.web.cors.CorsConfiguration
//import org.springframework.web.cors.CorsConfigurationSource
//import org.springframework.web.cors.UrlBasedCorsConfigurationSource
//
//@Configuration
//class CorsConfig {
//
//    @Bean
//    fun corsConfigurationSource(): CorsConfigurationSource {
//        val configuration = CorsConfiguration()
//
//        // 允许的源
//        configuration.allowedOrigins = listOf(
//            "http://localhost:5173",
//            "http://localhost:3000",
//            "http://127.0.0.1:5173",
//            "http://127.0.0.1:3000"
//        )
//
//        // 允许的方法
//        configuration.allowedMethods = listOf("GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH")
//
//        // 允许的请求头
//        configuration.allowedHeaders = listOf("*")
//
//        // 允许暴露的响应头
//        configuration.exposedHeaders = listOf("Authorization", "Content-Type")
//
//        // 是否允许携带凭证
//        configuration.allowCredentials = true
//
//        // 预检请求的有效期
//        configuration.maxAge = 3600
//
//        val source = UrlBasedCorsConfigurationSource()
//        source.registerCorsConfiguration("/**", configuration)
//
//        return source
//    }
//}