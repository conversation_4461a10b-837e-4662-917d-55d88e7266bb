package io.cliveyou.claudecodeproxybackend.config

import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.boot.jdbc.DataSourceBuilder
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.data.jpa.repository.config.EnableJpaRepositories
import org.springframework.orm.jpa.JpaTransactionManager
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean
import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.annotation.EnableTransactionManagement
import jakarta.persistence.EntityManagerFactory
import javax.sql.DataSource

/**
 * 多数据源配置
 * 主数据源：PostgreSQL（用于应用主要业务数据）
 * 辅助数据源：MySQL（用于同步GCP channels表数据）
 * Azure数据源：MySQL（用于同步Azure channels表数据）
 */
@Configuration
@EnableTransactionManagement
class DataSourceConfig {

    /**
     * 主数据源配置 - PostgreSQL
     */
    @Configuration
    @EnableTransactionManagement
    @EnableJpaRepositories(
        basePackages = ["io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.repository"],
        entityManagerFactoryRef = "pgEntityManagerFactory",
        transactionManagerRef = "pgTransactionManager"
    )
    class PgDataSourceConfig {

        @Primary
        @Bean(name = ["pgDataSource"])
        @ConfigurationProperties(prefix = "spring.datasource.pg")
        fun pgDataSource(): DataSource {
            return DataSourceBuilder.create().build()
        }

        @Primary
        @Bean(name = ["pgEntityManagerFactory"])
        fun pgEntityManagerFactory(
            @Qualifier("pgDataSource") dataSource: DataSource,
            builder: EntityManagerFactoryBuilder
        ): LocalContainerEntityManagerFactoryBean {
            return builder
                .dataSource(dataSource)
                .packages("io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity")
                .persistenceUnit("pgPU")
                .build()
        }

        @Primary
        @Bean(name = ["pgTransactionManager"])
        fun pgTransactionManager(
            @Qualifier("pgEntityManagerFactory") emf: EntityManagerFactory
        ): PlatformTransactionManager {
            return JpaTransactionManager(emf)
        }
    }

    /**
     * 辅助数据源配置 - MySQL
     */
    @Configuration
    @EnableTransactionManagement
    @EnableJpaRepositories(
        basePackages = ["io.cliveyou.claudecodeproxybackend.sync.infrastructure.repository"],
        entityManagerFactoryRef = "mysqlEntityManagerFactory",
        transactionManagerRef = "mysqlTransactionManager"
    )
    class MysqlDataSourceConfig {

        @Bean(name = ["mysqlDataSource"])
        @ConfigurationProperties(prefix = "spring.datasource.mysql")
        fun mysqlDataSource(): com.zaxxer.hikari.HikariDataSource = com.zaxxer.hikari.HikariDataSource()

        @Bean(name = ["mysqlEntityManagerFactory"])
        fun mysqlEntityManagerFactory(
            @Qualifier("mysqlDataSource") dataSource: DataSource,
            builder: EntityManagerFactoryBuilder
        ): LocalContainerEntityManagerFactoryBean {
            return builder
                .dataSource(dataSource)
                .packages("io.cliveyou.claudecodeproxybackend.sync.infrastructure.entity")
                .persistenceUnit("mysqlPU")
                .build()
        }

        @Bean(name = ["mysqlTransactionManager"])
        fun mysqlTransactionManager(
            @Qualifier("mysqlEntityManagerFactory") emf: EntityManagerFactory
        ): PlatformTransactionManager {
            return JpaTransactionManager(emf)
        }

    }

    /**
     * Azure数据源配置 - MySQL
     */
    @Configuration
    @EnableTransactionManagement
    @EnableJpaRepositories(
        basePackages = ["io.cliveyou.claudecodeproxybackend.azure.infrastructure.repository"],
        entityManagerFactoryRef = "azureEntityManagerFactory",
        transactionManagerRef = "azureTransactionManager"
    )
    class AzureDataSourceConfig {

        @Bean(name = ["azureDataSource"])
        @ConfigurationProperties(prefix = "spring.datasource.azure")
        fun azureDataSource(): DataSource {
            return DataSourceBuilder.create().build()
        }

        @Bean(name = ["azureEntityManagerFactory"])
        fun azureEntityManagerFactory(
            @Qualifier("azureDataSource") dataSource: DataSource,
            builder: EntityManagerFactoryBuilder
        ): LocalContainerEntityManagerFactoryBean {
            return builder
                .dataSource(dataSource)
                .packages("io.cliveyou.claudecodeproxybackend.azure.infrastructure.entity")
                .persistenceUnit("azurePU")
                .build()
        }

        @Bean(name = ["azureTransactionManager"])
        fun azureTransactionManager(
            @Qualifier("azureEntityManagerFactory") emf: EntityManagerFactory
        ): PlatformTransactionManager {
            return JpaTransactionManager(emf)
        }
    }
}