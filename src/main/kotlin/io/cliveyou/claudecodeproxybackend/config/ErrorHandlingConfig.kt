package io.cliveyou.claudecodeproxybackend.config

import org.springframework.boot.autoconfigure.web.WebProperties
import org.springframework.boot.web.reactive.error.DefaultErrorAttributes
import org.springframework.boot.web.reactive.error.ErrorAttributes
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.core.annotation.Order
import org.springframework.web.reactive.config.WebFluxConfigurer

/**
 * 错误处理配置
 */
@Configuration
class ErrorHandlingConfig : WebFluxConfigurer {
    
    @Bean
    @Order(-1)
    fun errorAttributes(): ErrorAttributes {
        return DefaultErrorAttributes()
    }
    
    @Bean
    fun webProperties(): WebProperties {
        return WebProperties()
    }
}