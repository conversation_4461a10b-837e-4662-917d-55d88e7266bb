package io.cliveyou.claudecodeproxybackend.config

import io.netty.channel.ChannelOption
import io.netty.handler.timeout.ReadTimeoutHandler
import io.netty.handler.timeout.WriteTimeoutHandler
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.client.reactive.ReactorClientHttpConnector
import org.springframework.http.codec.ClientCodecConfigurer
import org.springframework.web.reactive.function.client.ExchangeStrategies
import org.springframework.web.reactive.function.client.WebClient
import reactor.netty.Connection
import reactor.netty.http.client.HttpClient
import reactor.netty.resources.ConnectionProvider
import java.time.Duration
import java.util.concurrent.TimeUnit
import java.util.function.Consumer

@Configuration
class WebClientConfig {
    @Bean
    fun httpWebClient(): WebClient {
        // 创建连接池
        val provider = ConnectionProvider.builder("hwc-cos")
            .maxConnections(10000) // 设置最大连接数
            .maxIdleTime(Duration.ofSeconds(20)) // 设置连接的最大空闲时间
            .maxLifeTime(Duration.ofSeconds(60)) // 设置连接的最大生命周期
            .pendingAcquireTimeout(Duration.ofSeconds(60)) // 设置等待获取连接的超时时间
            .build()

        val size = 16 * 1024 * 1024

        val strategies = ExchangeStrategies.builder()
            .codecs(Consumer { codecs: ClientCodecConfigurer? -> codecs!!.defaultCodecs().maxInMemorySize(size) })
            .build()

        // 配置HttpClient
        val httpClient = HttpClient.create(provider)
            .option<Int?>(ChannelOption.CONNECT_TIMEOUT_MILLIS, 15000) // 设置连接超时为5秒
            .doOnConnected(Consumer { conn: Connection? ->
                conn!!
                    .addHandlerLast(ReadTimeoutHandler(6000, TimeUnit.SECONDS)) // 设置读取超时为10秒
                    .addHandlerLast(WriteTimeoutHandler(6000, TimeUnit.SECONDS))
            }) // 设置写入超时为10秒

        // 创建WebClient
        return WebClient.builder()
            .exchangeStrategies(strategies)
            .clientConnector(ReactorClientHttpConnector(httpClient))
            .build()
    }
}