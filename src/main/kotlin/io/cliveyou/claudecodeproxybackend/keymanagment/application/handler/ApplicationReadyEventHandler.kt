package io.cliveyou.claudecodeproxybackend.keymanagment.application.handler

import io.cliveyou.claudecodeproxybackend.keymanagment.domain.KeyManageService
import io.cliveyou.claudecodeproxybackend.keymanagment.domain.KeyWithSchedulerConfig
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.CommonKeyStatus
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.repository.CommonTokenKeyRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
class ApplicationReadyEventHandler(
    private val keyManageService: KeyManageService,
    private val commonTokenKeyRepository: CommonTokenKeyRepository,
) {

    companion object {
        private val log = KotlinLogging.logger {}
    }

    @EventListener(ApplicationReadyEvent::class)
    fun handleApplicationReadyEvent() {
        log.info { "Initializing KeyManageService with active keys from database" }

        try {
            // 查询所有激活状态的密钥
            val activeKeys = commonTokenKeyRepository.findByStatus(CommonKeyStatus.ACTIVE)
            log.info { "Found ${activeKeys.size} active keys in database" }

            // 按模型分组处理
            val modelKeysMapping = mutableMapOf<String, MutableList<KeyWithSchedulerConfig>>()

            // 处理每个密钥，按支持的模型分组
            activeKeys.forEach { key ->
                // 为每个密钥支持的模型创建 KeyWithSchedulerConfig
                key.supportModels.forEach { modelName ->
                    val keyWithConfig = KeyWithSchedulerConfig(
                        commonTokenKey = key,
                        weight = key.weight.toDouble(),
                        windowSize = key.windowSize,
                        epsilon = key.epsilon.toDouble(),
                        errThreshold = key.errThreshold.toDouble(),
                        minSampleSize = key.minSampleSize
                    )

                    // 添加到对应模型的密钥列表中
                    modelKeysMapping.getOrPut(modelName) { mutableListOf() }.add(keyWithConfig)
                }
            }
            
            log.info { "Created key mappings for ${modelKeysMapping.size} models" }

            // 加载到KeyManageService
            if (modelKeysMapping.isNotEmpty()) {
                keyManageService.loadAllModelKeys(modelKeysMapping)
            }

            val totalKeys = modelKeysMapping.values.sumOf { it.size }
            log.info { "Successfully initialized KeyManageService with $totalKeys total keys across ${modelKeysMapping.size} models" }

        } catch (e: Exception) {
            log.error(e) { "Failed to initialize KeyManageService" }
            throw e
        }
    }
}
