package io.cliveyou.claudecodeproxybackend.keymanagment.application.service

import io.cliveyou.claudecodeproxybackend.keymanagment.domain.KeyChannel
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.remote.test.ApiTestResult
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.remote.test.ClaudeTestRemoteService
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.remote.test.OpenAITestRemoteService
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.repository.CommonTokenKeyRepository
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.repository.SpConsoleSystemModelRepository
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono

@Service
@Suppress("BlockingMethodInNonBlockingContext")
class KeyTestService(
    private val modelRepository: SpConsoleSystemModelRepository,
    private val claudeTestRemoteService: ClaudeTestRemoteService,
    private val openAITestRemoteService: OpenAITestRemoteService,
    private val commonTokenKeyRepository: CommonTokenKeyRepository,
) {


    fun testKey(channel: String, id: Long, model: String): Mono<ApiTestResult> {
        val keyChannel = KeyChannel.of(channel)
        if (keyChannel == null) {
            throw IllegalArgumentException("Channel doesn't exist")
        }
        return when (keyChannel) {
            KeyChannel.ClaudeCode -> {
                val key = commonTokenKeyRepository.findByIdOrNull(id) ?: throw IllegalArgumentException("密钥不存在")
                if (key.type != KeyChannel.ClaudeCode) {
                    throw IllegalArgumentException("密钥类型不匹配")
                }
                claudeTestRemoteService.testKey(key.accessToken!!, model)
            }

            KeyChannel.Openai -> {
                val key = commonTokenKeyRepository.findByIdOrNull(id) ?: throw IllegalArgumentException("密钥不存在")
                openAITestRemoteService.testKey(key.accessToken!!, model,key.domain)
            }

            KeyChannel.Agent -> {
                val key = commonTokenKeyRepository.findByIdOrNull(id) ?: throw IllegalArgumentException("密钥不存在")
                if ("claude" in model) {
                    claudeTestRemoteService.testKey(key.accessToken!!, model)
                } else {
                    openAITestRemoteService.testKey(key.accessToken!!, model,key.domain)
                }
            }

            else -> {
                throw UnsupportedOperationException("Channel $channel is not supported for testing yet")
            }
        }
    }

}