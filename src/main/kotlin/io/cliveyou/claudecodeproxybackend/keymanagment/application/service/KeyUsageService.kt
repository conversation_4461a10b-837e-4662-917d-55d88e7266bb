package io.cliveyou.claudecodeproxybackend.keymanagment.application.service

import io.cliveyou.claudecodeproxybackend.keymanagment.domain.AuthKey
import io.cliveyou.claudecodeproxybackend.keymanagment.domain.KeyChannel
import io.cliveyou.claudecodeproxybackend.keymanagment.domain.KeyManageService
import io.cliveyou.claudecodeproxybackend.keymanagment.facade.controller.*
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.CommonTokenKey
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.CommonKeyStatus
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.toAuthKey
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.repository.CommonTokenKeyRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import kotlin.time.Duration.Companion.minutes

/**
 * 密钥使用服务
 * 展示如何使用新的密钥管理系统
 */
@Service
class KeyUsageService(
    private val keyManageService: KeyManageService,
    private val commonTokenKeyRepository: CommonTokenKeyRepository
) {
    
    companion object {
        private val log = KotlinLogging.logger {}
    }

    /**
     * 获取模型的最优密钥并执行API调用
     * @param modelName 模型名称
     * @param channels 可选的渠道列表，如果指定则只从这些渠道的密钥中选择
     * @param apiCall API调用函数
     * @return API调用结果
     */
    suspend fun <T> executeWithBestKey(
        modelName: String,
        channels: List<KeyChannel>? = null,
        apiCall: suspend (CommonTokenKey) -> T
    ): T {
        val startTime = System.currentTimeMillis()

        // 获取最优密钥
        val selectedKey = keyManageService.findBestAvailableKey(modelName, channels)
        val channelInfo = if (channels.isNullOrEmpty()) "" else ", channels: $channels"
        log.info { "Selected key ${selectedKey.id} (channel: ${selectedKey.type}) for model $modelName$channelInfo" }

        return try {
            // 执行API调用
            val result = apiCall(selectedKey)
            
            // 记录成功结果
            keyManageService.recordKeyUsageResult(modelName, selectedKey.id!!, true, false)
            
            val duration = System.currentTimeMillis() - startTime
            log.info { "API call successful for model $modelName with key ${selectedKey.id} | Duration: ${duration}ms" }
            
            result
        } catch (e: Exception) {
            val duration = System.currentTimeMillis() - startTime
            log.error(e) { "API call failed for model $modelName with key ${selectedKey.id} | Duration: ${duration}ms" }

            // 如果是严重错误（如认证失败），标记密钥需要删除
            val needDeleted = isSeriousError(e)
            if (needDeleted) {
                log.warn { "Marking key ${selectedKey.id} for deletion due to serious error: ${e.message}" }
            }

            // 记录失败结果，如果是严重错误则标记需要删除
            keyManageService.recordKeyUsageResult(modelName, selectedKey.id!!, false, needDeleted)

            throw e
        }
    }

    /**
     * 兼容性方法：获取模型的最优密钥并执行API调用（使用AuthKey）
     * @param modelName 模型名称
     * @param channels 可选的渠道列表，如果指定则只从这些渠道的密钥中选择
     * @param apiCall API调用函数
     * @return API调用结果
     */
    @Deprecated("Use executeWithBestKey with CommonTokenKey instead")
    suspend fun <T> executeWithBestKeyAuthKey(
        modelName: String,
        channels: List<KeyChannel>? = null,
        apiCall: suspend (AuthKey) -> T
    ): T {
        return executeWithBestKey(modelName, channels) { commonTokenKey ->
            val authKey = commonTokenKey.toAuthKey()
            if (authKey != null) {
                apiCall(authKey)
            } else {
                throw IllegalStateException("Failed to convert CommonTokenKey to AuthKey for key ${commonTokenKey.id}")
            }
        }
    }

    /**
     * 批量执行API调用，自动选择不同的密钥
     * @param modelName 模型名称
     * @param channels 可选的渠道列表，如果指定则只从这些渠道的密钥中选择
     * @param requests 请求列表
     * @param apiCall API调用函数
     * @return 结果列表
     */
    suspend fun <T, R> executeBatch(
        modelName: String,
        channels: List<KeyChannel>? = null,
        requests: List<T>,
        apiCall: suspend (CommonTokenKey, T) -> R
    ): List<R> {
        val results = mutableListOf<R>()

        requests.forEach { request ->
            try {
                val result = executeWithBestKey(modelName, channels) { key ->
                    apiCall(key, request)
                }
                results.add(result)
            } catch (e: Exception) {
                val channelInfo = if (channels.isNullOrEmpty()) "" else ", channels: $channels"
                log.error(e) { "Batch request failed for model $modelName$channelInfo" }
                throw e
            }
        }
        
        return results
    }

    /**
     * 获取模型的密钥统计信息
     * @param modelName 模型名称
     * @return 统计信息
     */
    fun getModelKeyStats(modelName: String): Map<String, Any>? {
        return keyManageService.getModelKeyStats(modelName)
    }

    /**
     * 更新密钥权重
     * @param keyId 密钥ID
     * @param weight 新权重
     */
    fun updateKeyWeight(keyId: Long, weight: Double) {
        keyManageService.updateKeyWeight(keyId, weight)
        log.info { "Updated weight for key $keyId to $weight" }
    }

    /**
     * 调整模型的调度器配置
     * @param modelName 模型名称
     * @param epsilon 探索率
     * @param errThreshold 错误阈值
     * @param windowSize 滑动窗口大小
     * @param minSampleSize 最小样本数阈值
     */
    fun adjustSchedulerConfig(
        modelName: String,
        epsilon: Double? = null,
        errThreshold: Double? = null,
        windowSize: Int? = null,
        minSampleSize: Int? = null
    ) {
        keyManageService.updateSchedulerConfig(modelName) {
            epsilon?.let { this.epsilon = it }
            errThreshold?.let { this.errThreshold = it }
            windowSize?.let { this.windowSize = it }
            minSampleSize?.let { this.minSampleSize = it }
        }
        log.info { "Updated scheduler config for model $modelName" }
    }

    /**
     * 手动记录密钥使用结果
     * @param modelName 模型名称
     * @param keyId 密钥ID
     * @param success 是否成功
     * @param needDeleted 是否需要删除密钥（密钥失效时为true）
     */
    fun recordKeyUsageResult(modelName: String, keyId: Long, success: Boolean, needDeleted: Boolean = false) {
        keyManageService.recordKeyUsageResult(modelName, keyId, success, needDeleted)
        log.info { "Manually recorded key usage result for model: $modelName, keyId: $keyId, success: $success, needDeleted: $needDeleted" }
    }

    /**
     * 获取密钥监控概览数据
     */
    fun getKeyMonitoringOverview(): KeyMonitoringOverview {
        val startTime = System.currentTimeMillis()

        try {
            // 获取所有密钥
            val allKeys = commonTokenKeyRepository.findAll()

            // 统计各状态密钥数量
            val totalKeys = allKeys.size
            val activeKeys = allKeys.count { it.status == CommonKeyStatus.ACTIVE }
            val disabledKeys = allKeys.count { it.status == CommonKeyStatus.DISABLED }
            val authFailedKeys = allKeys.count { it.status == CommonKeyStatus.AUTH_FAILED }

            // 统计渠道分布
            val channelDistribution = allKeys.groupBy { it.type.name }
                .mapValues { it.value.size }

            // 获取所有模型统计
            val allModelsStats = getAllModelsKeyStats()
            val totalModels = allModelsStats.size

            // 计算平均成功率
            val avgSuccessRate = if (allModelsStats.isNotEmpty()) {
                allModelsStats.values.map { it.avgSuccessRate }.average()
            } else 0.0

            // 计算总请求数
            val totalRequests = allModelsStats.values.sumOf { it.totalRequests }

            val duration = System.currentTimeMillis() - startTime
            log.info { "Generated key monitoring overview | Duration: ${duration}ms" }

            return KeyMonitoringOverview(
                totalKeys = totalKeys,
                activeKeys = activeKeys,
                disabledKeys = disabledKeys,
                authFailedKeys = authFailedKeys,
                totalModels = totalModels,
                channelDistribution = channelDistribution,
                avgSuccessRate = avgSuccessRate,
                totalRequests = totalRequests,
                lastUpdateTime = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
            )
        } catch (e: Exception) {
            val duration = System.currentTimeMillis() - startTime
            log.error(e) { "Failed to get key monitoring overview | Duration: ${duration}ms" }
            throw e
        }
    }

    /**
     * 获取所有模型的密钥统计信息
     */
    fun getAllModelsKeyStats(): Map<String, ModelKeyStats> {
        val startTime = System.currentTimeMillis()

        try {
            val result = mutableMapOf<String, ModelKeyStats>()

            // 获取所有模型的统计信息
            keyManageService.getAllModelNames().forEach { modelName ->
                val modelStats = getModelKeyStats(modelName)
                if (modelStats != null) {
                    result[modelName] = convertToModelKeyStats(modelName, modelStats)
                }
            }

            val duration = System.currentTimeMillis() - startTime
            log.info { "Generated all models key stats for ${result.size} models | Duration: ${duration}ms" }

            return result
        } catch (e: Exception) {
            val duration = System.currentTimeMillis() - startTime
            log.error(e) { "Failed to get all models key stats | Duration: ${duration}ms" }
            throw e
        }
    }

    /**
     * 获取密钥性能趋势数据
     */
    fun getKeyPerformanceTrends(hours: Int): List<KeyPerformanceTrend> {
        val startTime = System.currentTimeMillis()

        try {
            val trends = mutableListOf<KeyPerformanceTrend>()
            val allModelsStats = getAllModelsKeyStats()
            val now = LocalDateTime.now()

            // 为每个模型的每个密钥生成当前时刻的数据点
            // 注意：这里只生成当前时刻的数据，因为我们没有历史数据存储
            // 在实际生产环境中，应该从时序数据库或监控系统获取历史数据
            allModelsStats.forEach { (modelName, modelStats) ->
                modelStats.keyStats.forEach { (keyIdStr, keyStats) ->
                    // 只生成当前时刻的数据点，表示最新状态
                    trends.add(
                        KeyPerformanceTrend(
                            timestamp = now.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                            keyId = keyIdStr.toLong(),
                            modelName = modelName,
                            successRate = keyStats.successRate,
                            requestCount = keyStats.requestCount.toInt(),
                            avgResponseTime = null, // 需要从实际监控数据获取
                            errorRate = keyStats.errorRate,
                            channel = keyStats.channel
                        )
                    )
                }
            }

            val duration = System.currentTimeMillis() - startTime
            log.info { "Generated ${trends.size} key performance trend data points (current snapshot) | Duration: ${duration}ms" }

            return trends.sortedBy { it.timestamp }
        } catch (e: Exception) {
            val duration = System.currentTimeMillis() - startTime
            log.error(e) { "Failed to get key performance trends | Duration: ${duration}ms" }
            throw e
        }
    }

    /**
     * 将内部统计数据转换为API响应格式
     */
    private fun convertToModelKeyStats(modelName: String, rawStats: Map<String, Any>): ModelKeyStats {
        val keyStats = mutableMapOf<String, KeyStatInfo>()
        var totalRequests = 0L
        var totalSuccessCount = 0L
        var bestKeyId: Long? = null
        var worstKeyId: Long? = null
        var bestScore = Double.MIN_VALUE
        var worstScore = Double.MAX_VALUE

        rawStats.forEach { (keyId, stats) ->
            if (stats is Map<*, *>) {
                val weight = stats["weight"] as? Double ?: 1.0
                val errorRate = stats["errorRate"] as? Double ?: 0.0
                val score = stats["score"] as? Double ?: 0.0

                // 使用真实的请求统计数据
                val requestCount = (stats["totalRequests"] as? Int)?.toLong() ?: 0L
                val successCount = (stats["successRequests"] as? Int)?.toLong() ?: 0L

                totalRequests += requestCount
                totalSuccessCount += successCount

                // 找出最佳和最差密钥
                if (score > bestScore) {
                    bestScore = score
                    bestKeyId = keyId.toLong()
                }
                if (score < worstScore) {
                    worstScore = score
                    worstKeyId = keyId.toLong()
                }

                // 获取密钥详细信息
                val keyInfo = commonTokenKeyRepository.findById(keyId.toLong()).orElse(null)

                keyStats[keyId] = KeyStatInfo(
                    keyId = keyId,
                    weight = weight,
                    errorRate = errorRate,
                    score = score,
                    requestCount = requestCount,
                    successCount = successCount,
                    successRate = if (requestCount > 0) successCount.toDouble() / requestCount.toDouble() else 0.0,
                    channel = keyInfo?.type?.name ?: "Unknown",
                    name = keyInfo?.name
                )
            }
        }

        val avgSuccessRate = if (totalRequests > 0) {
            totalSuccessCount.toDouble() / totalRequests.toDouble()
        } else 0.0

        // 获取真实的调度器配置
        val schedulerConfig = keyManageService.getSchedulerConfig(modelName)

        return ModelKeyStats(
            modelName = modelName,
            keyCount = keyStats.size,
            avgSuccessRate = avgSuccessRate,
            totalRequests = totalRequests,
            bestKeyId = bestKeyId,
            worstKeyId = worstKeyId,
            schedulerConfig = SchedulerConfigInfo(
                epsilon = schedulerConfig?.epsilon ?: 0.05,
                errThreshold = schedulerConfig?.errThreshold ?: 0.5,
                windowSize = schedulerConfig?.windowSize ?: 100,
                minSampleSize = schedulerConfig?.minSampleSize ?: 5
            ),
            keyStats = keyStats
        )
    }

    /**
     * 判断是否为严重错误，需要冻结密钥
     */
    private fun isSeriousError(e: Exception): Boolean {
        val message = e.message?.lowercase() ?: ""
        return message.contains("unauthorized") ||
               message.contains("forbidden") ||
               message.contains("invalid") ||
               message.contains("expired") ||
               message.contains("authentication")
    }
}
