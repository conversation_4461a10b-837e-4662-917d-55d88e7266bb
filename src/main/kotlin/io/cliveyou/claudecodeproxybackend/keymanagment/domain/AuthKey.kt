package io.cliveyou.claudecodeproxybackend.keymanagment.domain

interface AuthKey {
    val id: Long
    val token: String
    val channel: KeyChannel
}

data class CommonAuthKey(
    override val id: Long,
    override val token: String,
    override val channel: KeyChannel,
    val modelMapping: Map<String, String>? = null,
    val metadata: Map<String, String>? = null
) : AuthKey


enum class KeyChannel {
    ClaudeCode,
    Openai,
    Azure,
    Agent,
    Aws,
    Anthropic,
    GoogleAiStudio,
    GoogleVertexAI,
    GoogleSearch,
    BingSearch,
    Ark
    ;


    companion object {

        fun of(channel: String): KeyChannel? {
            return KeyChannel.entries.firstOrNull { it.name.lowercase() == channel.lowercase() }
        }
    }
}
