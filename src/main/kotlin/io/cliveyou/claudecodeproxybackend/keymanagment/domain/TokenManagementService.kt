package io.cliveyou.claudecodeproxybackend.keymanagment.domain

import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.*
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.repository.ClaudeCodeTokenRefreshInfoRepository
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.repository.CommonTokenKeyRepository
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.repository.GoogleVertexAITokenRefreshInfoRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.time.LocalDateTime

@Service
class TokenManagementService(
    private val commonTokenKeyRepository: CommonTokenKeyRepository,
    private val claudeCodeTokenRefreshInfoRepository: ClaudeCodeTokenRefreshInfoRepository,
    private val googleVertexAITokenRefreshInfoRepository: GoogleVertexAITokenRefreshInfoRepository,
) {

    /**
     * 创建新的通用密钥
     * @param tokenKey 通用密钥信息
     * @param claudeRefreshInfo 如果是 Claude Code 类型，提供刷新信息
     * @param googleRefreshInfo 如果是 Google Vertex AI 类型，提供刷新信息
     * @return 创建的密钥
     */
    @Transactional
    fun createTokenKey(
        tokenKey: CommonTokenKey,
        claudeRefreshInfo: ClaudeCodeTokenRefreshInfo? = null,
        googleRefreshInfo: GoogleVertexAITokenRefreshInfo? = null,
    ): CommonTokenKey {
        val startTime = System.currentTimeMillis()
        
        // 保存通用密钥
        val savedKey = commonTokenKeyRepository.save(tokenKey)
        log.info { "Create Token | Created CommonTokenKey with id: ${savedKey.id}, type: ${savedKey.type}" }
        
        // 如果是 Claude Code 类型且提供了刷新信息，保存刷新信息
        if (savedKey.type == KeyChannel.ClaudeCode && claudeRefreshInfo != null) {
            val refreshInfoToSave = claudeRefreshInfo.copy(
                commonKeyId = savedKey.id!!,
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            )
            claudeCodeTokenRefreshInfoRepository.save(refreshInfoToSave)
            log.info { "Create Token | Created ClaudeCodeTokenRefreshInfo for CommonTokenKey: ${savedKey.id}" }
        }
        
        // 如果是 Google Vertex AI 类型且提供了刷新信息，保存刷新信息
        if (savedKey.type == KeyChannel.GoogleVertexAI && googleRefreshInfo != null) {
            val refreshInfoToSave = googleRefreshInfo.copy(
                commonKeyId = savedKey.id!!,
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            )
            googleVertexAITokenRefreshInfoRepository.save(refreshInfoToSave)
            log.info { "Create Token | Created GoogleVertexAITokenRefreshInfo for CommonTokenKey: ${savedKey.id}" }
        }
        
        val duration = System.currentTimeMillis() - startTime
        log.info { "Create Token | Successfully created token key | Duration: ${duration}ms" }
        
        return savedKey
    }

    /**
     * 更新通用密钥
     * @param keyId 密钥ID
     * @param updates 更新的字段
     * @param refreshInfoUpdates 如果是 Claude Code 类型，提供刷新信息的更新
     * @return 更新后的密钥
     */
    @Transactional
    fun updateTokenKey(
        keyId: Long,
        updates: Map<String, Any>,
        refreshInfoUpdates: Map<String, Any>? = null,
    ): CommonTokenKey {
        val startTime = System.currentTimeMillis()
        
        val existingKey = commonTokenKeyRepository.findById(keyId)
            .orElseThrow { IllegalArgumentException("Token key not found with id: $keyId") }
        
        // 更新通用密钥字段
        val updatedKey = existingKey.copy(
            accessToken = updates["accessToken"] as? String ?: existingKey.accessToken,
            domain = updates["domain"] as? String ?: existingKey.domain,
            status = updates["status"] as? CommonKeyStatus ?: existingKey.status,
            name = updates["name"] as? String ?: existingKey.name,
            supportModels = updates["supportModels"] as? List<String> ?: existingKey.supportModels,
            autoDisable = updates["autoDisable"] as? Boolean ?: existingKey.autoDisable,
            modelMapping = updates["modelMapping"] as? String ?: existingKey.modelMapping,
            quota = updates["quota"] as? BigDecimal ?: existingKey.quota,
            weight = updates["weight"] as? BigDecimal ?: existingKey.weight,
            windowSize = updates["windowSize"] as? Int ?: existingKey.windowSize,
            epsilon = updates["epsilon"] as? BigDecimal ?: existingKey.epsilon,
            errThreshold = updates["errThreshold"] as? BigDecimal ?: existingKey.errThreshold,
            updatedAt = LocalDateTime.now()
        )
        
        val savedKey = commonTokenKeyRepository.save(updatedKey)
        log.info { "Update Token | Updated CommonTokenKey with id: $keyId" }
        
        // 如果是 Claude Code 类型且提供了刷新信息更新
        if (savedKey.type == KeyChannel.ClaudeCode && refreshInfoUpdates != null) {
            val refreshInfo = claudeCodeTokenRefreshInfoRepository.findByCommonKeyId(keyId)
            
            if (refreshInfo.isPresent) {
                val existingRefreshInfo = refreshInfo.get()
                val updatedRefreshInfo = existingRefreshInfo.copy(
                    refreshToken = refreshInfoUpdates["refreshToken"] as? String ?: existingRefreshInfo.refreshToken,
                    expiresAt = refreshInfoUpdates["expiresAt"] as? Long ?: existingRefreshInfo.expiresAt,
                    clientId = refreshInfoUpdates["clientId"] as? String ?: existingRefreshInfo.clientId,
                    email = refreshInfoUpdates["email"] as? String ?: existingRefreshInfo.email,
                    accountType = refreshInfoUpdates["accountType"] as? ClaudeType ?: existingRefreshInfo.accountType,
                    updatedAt = LocalDateTime.now()
                )
                claudeCodeTokenRefreshInfoRepository.save(updatedRefreshInfo)
                log.info { "Update Token | Updated ClaudeCodeTokenRefreshInfo for CommonTokenKey: $keyId" }
            }
        }
        
        val duration = System.currentTimeMillis() - startTime
        log.info { "Update Token | Successfully updated token key | Duration: ${duration}ms" }
        
        return savedKey
    }

    /**
     * 删除通用密钥及其关联的刷新信息
     * @param keyId 密钥ID
     */
    @Transactional
    fun deleteTokenKey(keyId: Long) {
        val startTime = System.currentTimeMillis()
        
        val tokenKey = commonTokenKeyRepository.findById(keyId)
            .orElseThrow { IllegalArgumentException("Token key not found with id: $keyId") }
        
        // 如果是 Claude Code 类型，先删除刷新信息
        if (tokenKey.type == KeyChannel.ClaudeCode) {
            claudeCodeTokenRefreshInfoRepository.deleteByCommonKeyId(keyId)
            log.info { "Delete Token | Deleted ClaudeCodeTokenRefreshInfo for CommonTokenKey: $keyId" }
        }
        
        // 如果是 Google Vertex AI 类型，先删除刷新信息
        if (tokenKey.type == KeyChannel.GoogleVertexAI) {
            googleVertexAITokenRefreshInfoRepository.deleteByCommonKeyId(keyId)
            log.info { "Delete Token | Deleted GoogleVertexAITokenRefreshInfo for CommonTokenKey: $keyId" }
        }
        
        // 删除通用密钥
        commonTokenKeyRepository.deleteById(keyId)
        log.info { "Delete Token | Deleted CommonTokenKey with id: $keyId" }
        
        val duration = System.currentTimeMillis() - startTime
        log.info { "Delete Token | Successfully deleted token key | Duration: ${duration}ms" }
    }

    /**
     * 获取带有刷新信息的 Claude Code 密钥
     * @param keyId 密钥ID
     * @return 密钥和刷新信息
     */
    fun getClaudeCodeKeyWithRefreshInfo(keyId: Long): Pair<CommonTokenKey, ClaudeCodeTokenRefreshInfo?> {
        val tokenKey = commonTokenKeyRepository.findById(keyId)
            .orElseThrow { IllegalArgumentException("Token key not found with id: $keyId") }
        
        val refreshInfo = if (tokenKey.type == KeyChannel.ClaudeCode) {
            claudeCodeTokenRefreshInfoRepository.findByCommonKeyId(keyId).orElse(null)
        } else {
            null
        }
        
        return Pair(tokenKey, refreshInfo)
    }

    /**
     * 刷新 Claude Code 访问令牌
     * @param keyId 密钥ID
     * @param newAccessToken 新的访问令牌
     * @param newExpiresAt 新的过期时间
     * @param newRefreshToken 新的刷新令牌（可选）
     */
    @Transactional
    fun refreshClaudeCodeAccessToken(
        keyId: Long,
        newAccessToken: String,
        newExpiresAt: Long? = null,
        newRefreshToken: String? = null
    ): CommonTokenKey {
        val startTime = System.currentTimeMillis()

        val tokenKey = commonTokenKeyRepository.findById(keyId)
            .orElseThrow { IllegalArgumentException("Token key not found with id: $keyId") }

        if (tokenKey.type != KeyChannel.ClaudeCode) {
            throw IllegalArgumentException("Token key is not Claude Code type")
        }

        // 更新访问令牌
        val updatedKey = tokenKey.copy(
            accessToken = newAccessToken,
            updatedAt = LocalDateTime.now()
        )
        val savedKey = commonTokenKeyRepository.save(updatedKey)

        // 更新刷新信息中的过期时间、刷新令牌和最后刷新时间
        val refreshInfo = claudeCodeTokenRefreshInfoRepository.findByCommonKeyId(keyId)
        if (refreshInfo.isPresent) {
            val existingRefreshInfo = refreshInfo.get()
            val updatedRefreshInfo = existingRefreshInfo.copy(
                refreshToken = newRefreshToken ?: existingRefreshInfo.refreshToken,
                expiresAt = newExpiresAt ?: existingRefreshInfo.expiresAt,
                lastRefreshTime = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            )
            claudeCodeTokenRefreshInfoRepository.save(updatedRefreshInfo)

            if (newRefreshToken != null) {
                log.info { "Refresh Token | Updated refresh token for key: $keyId" }
            }
        }

        val duration = System.currentTimeMillis() - startTime
        log.info { "Refresh Token | Successfully refreshed access token for key: $keyId | Duration: ${duration}ms" }

        return savedKey
    }

    /**
     * 查找需要刷新的 Claude Code 密钥
     * @param expirationThreshold 过期时间阈值（秒）
     * @return 需要刷新的密钥列表
     */
    fun findClaudeCodeKeysNeedingRefresh(expirationThreshold: Long): List<Pair<CommonTokenKey, ClaudeCodeTokenRefreshInfo>> {
        val refreshInfoList = claudeCodeTokenRefreshInfoRepository.findRefreshInfoExpiringBefore(expirationThreshold)
        
        return refreshInfoList.mapNotNull { refreshInfo ->
            commonTokenKeyRepository.findById(refreshInfo.commonKeyId).orElse(null)?.let { tokenKey ->
                if (tokenKey.status == CommonKeyStatus.ACTIVE) {
                    Pair(tokenKey, refreshInfo)
                } else {
                    null
                }
            }
        }
    }

    /**
     * 刷新 Google Vertex AI 访问令牌
     * @param keyId 密钥ID
     * @param newAccessToken 新的访问令牌
     * @param newExpiresAt 新的过期时间
     */
    @Transactional
    fun refreshGoogleVertexAIAccessToken(
        keyId: Long,
        newAccessToken: String,
        newExpiresAt: Long? = null,
    ): CommonTokenKey {
        val startTime = System.currentTimeMillis()
        
        val tokenKey = commonTokenKeyRepository.findById(keyId)
            .orElseThrow { IllegalArgumentException("Token key not found with id: $keyId") }
        
        if (tokenKey.type != KeyChannel.GoogleVertexAI) {
            throw IllegalArgumentException("Token key is not Google Vertex AI type")
        }
        
        // 更新访问令牌
        val updatedKey = tokenKey.copy(
            accessToken = newAccessToken,
            updatedAt = LocalDateTime.now()
        )
        val savedKey = commonTokenKeyRepository.save(updatedKey)
        
        // 更新刷新信息中的过期时间和最后刷新时间
        val refreshInfo = googleVertexAITokenRefreshInfoRepository.findByCommonKeyId(keyId)
        if (refreshInfo.isPresent && newExpiresAt != null) {
            val updatedRefreshInfo = refreshInfo.get().copy(
                expiresAt = newExpiresAt,
                lastRefreshTime = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            )
            googleVertexAITokenRefreshInfoRepository.save(updatedRefreshInfo)
        }
        
        val duration = System.currentTimeMillis() - startTime
        log.info { "Refresh Token | Successfully refreshed Google Vertex AI access token for key: $keyId | Duration: ${duration}ms" }
        
        return savedKey
    }

    /**
     * 查找需要刷新的 Google Vertex AI 密钥
     * @param expirationThreshold 过期时间阈值（秒）
     * @return 需要刷新的密钥列表
     */
    fun findGoogleVertexAIKeysNeedingRefresh(expirationThreshold: Long): List<Pair<CommonTokenKey, GoogleVertexAITokenRefreshInfo>> {
        val refreshInfoList = googleVertexAITokenRefreshInfoRepository.findRefreshInfoExpiringBefore(expirationThreshold)
        
        return refreshInfoList.mapNotNull { refreshInfo ->
            commonTokenKeyRepository.findById(refreshInfo.commonKeyId).orElse(null)?.let { tokenKey ->
                if (tokenKey.status == CommonKeyStatus.ACTIVE) {
                    Pair(tokenKey, refreshInfo)
                } else {
                    null
                }
            }
        }
    }

    /**
     * 获取带有刷新信息的 Google Vertex AI 密钥
     * @param keyId 密钥ID
     * @return 密钥和刷新信息
     */
    fun getGoogleVertexAIKeyWithRefreshInfo(keyId: Long): Pair<CommonTokenKey, GoogleVertexAITokenRefreshInfo?> {
        val tokenKey = commonTokenKeyRepository.findById(keyId)
            .orElseThrow { IllegalArgumentException("Token key not found with id: $keyId") }
        
        val refreshInfo = if (tokenKey.type == KeyChannel.GoogleVertexAI) {
            googleVertexAITokenRefreshInfoRepository.findByCommonKeyId(keyId).orElse(null)
        } else {
            null
        }
        
        return Pair(tokenKey, refreshInfo)
    }

    /**
     * 更新密钥状态
     * @param keyId 密钥ID
     * @param status 新状态
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    fun updateKeyStatus(keyId: Long, status: CommonKeyStatus) {
        val existingKey = commonTokenKeyRepository.findById(keyId).orElse(null)
        if (existingKey != null) {
            val updatedKey = existingKey.copy(
                status = status,
                updatedAt = LocalDateTime.now()
            )
            commonTokenKeyRepository.save(updatedKey)
            log.info { "Updated key status | keyId: $keyId, newStatus: $status" }
        } else {
            log.warn { "Key not found when updating status | keyId: $keyId, status: $status" }
        }
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}