package io.cliveyou.claudecodeproxybackend.keymanagment.facade.controller

import io.cliveyou.claudecodeproxybackend.common.response.PlatformResult
import io.cliveyou.claudecodeproxybackend.keymanagment.application.service.KeyUsageService
import io.cliveyou.claudecodeproxybackend.keymanagment.domain.KeyChannel
import io.cliveyou.claudecodeproxybackend.keymanagment.domain.KeyManageService
import io.cliveyou.claudecodeproxybackend.keymanagment.facade.dto.BestKeyResponse
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.toAuthKey
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.web.bind.annotation.CrossOrigin
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * 密钥统计信息控制器
 */
@CrossOrigin
@RestController
@RequestMapping("/api/v1/key-stats")
class KeyStatsController(
    private val keyUsageService: KeyUsageService,
    private val keyManageService: KeyManageService
) {
    
    companion object {
        private val log = KotlinLogging.logger {}
    }
    
    /**
     * 获取模型的密钥统计信息
     */
    @GetMapping("/model/{modelName}")
    fun getModelKeyStats(@PathVariable modelName: String): PlatformResult<Map<String, Any>?> {
        log.info { "Getting key stats for model: $modelName" }
        val stats = keyUsageService.getModelKeyStats(modelName)
        return PlatformResult.success(stats)
    }
    
    /**
     * 更新密钥权重
     */
    @PostMapping("/key/{keyId}/weight/{weight}")
    fun updateKeyWeight(
        @PathVariable keyId: Long,
        @PathVariable weight: Double
    ): PlatformResult<Boolean> {
        log.info { "Updating weight for key $keyId to $weight" }
        keyUsageService.updateKeyWeight(keyId, weight)
        return PlatformResult.success(true)
    }
    
    /**
     * 调整模型的调度器配置
     */
    @PostMapping("/model/{modelName}/config")
    fun adjustSchedulerConfig(
        @PathVariable modelName: String,
        @RequestBody config: SchedulerConfigRequest
    ): PlatformResult<Boolean> {
        log.info { "Adjusting scheduler config for model $modelName: $config" }
        keyUsageService.adjustSchedulerConfig(
            modelName = modelName,
            epsilon = config.epsilon,
            errThreshold = config.errThreshold,
            windowSize = config.windowSize,
            minSampleSize = config.minSampleSize
        )
        return PlatformResult.success(true)
    }

    /**
     * 获取指定模型的最优密钥
     * @param modelName 模型名称
     * @param channels 可选的渠道列表，多个渠道用逗号分隔
     * @return 选中的最优密钥信息
     */
    @GetMapping("/best-key/{modelName}")
    fun getBestAvailableKey(
        @PathVariable modelName: String,
        @RequestParam(required = false) channels: String?
    ): PlatformResult<BestKeyResponse> {
        log.info { "Getting best available key for model: $modelName, channels: $channels" }

        try {
            // 解析渠道参数
            val channelList = channels?.split(",")?.mapNotNull { channelStr ->
                val trimmedChannel = channelStr.trim()
                KeyChannel.of(trimmedChannel) ?: run {
                    log.warn { "Invalid channel: $trimmedChannel" }
                    null
                }
            }

            // 获取最优密钥
            val selectedKey = keyManageService.findBestAvailableKey(modelName, channelList)
            val authKey = selectedKey.toAuthKey()
                ?: throw IllegalStateException("Failed to convert selected key to auth key")

            // 构建响应
            val response = BestKeyResponse(
                id = authKey.id,
                token = authKey.token,
                channel = authKey.channel,
                modelMapping = authKey.modelMapping,
                metadata = authKey.metadata,
                name = selectedKey.name,
                domain = selectedKey.domain,
                selectedAt = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
            )

            log.info { "Successfully selected key ${authKey.id} (channel: ${authKey.channel}) for model $modelName" }
            return PlatformResult.success(response)

        } catch (e: IllegalStateException) {
            log.error(e) { "Failed to get best available key for model $modelName: ${e.message}" }
            return PlatformResult.error(e.message ?: "Failed to get best available key")
        } catch (e: Exception) {
            log.error(e) { "Unexpected error while getting best available key for model $modelName" }
            return PlatformResult.error("Internal server error")
        }
    }

    /**
     * 手动记录密钥使用结果，用于调度器学习
     * @param modelName 模型名称
     * @param keyId 密钥ID
     * @param request 使用结果请求
     * @return 操作结果
     */
    @PostMapping("/model/{modelName}/key/{keyId}/result")
    fun recordKeyUsageResult(
        @PathVariable modelName: String,
        @PathVariable keyId: Long,
        @RequestBody request: KeyUsageResultRequest
    ): PlatformResult<Boolean> {
        log.info { "Recording key usage result for model: $modelName, keyId: $keyId, success: ${request.success}, needDeleted: ${request.needDeleted}" }

        try {
            keyManageService.recordKeyUsageResult(modelName, keyId, request.success, request.needDeleted)
            log.info { "Successfully recorded key usage result for model: $modelName, keyId: $keyId, success: ${request.success}, needDeleted: ${request.needDeleted}" }
            return PlatformResult.success(true)
        } catch (e: Exception) {
            log.error(e) { "Failed to record key usage result for model: $modelName, keyId: $keyId" }
            return PlatformResult.error("Failed to record key usage result: ${e.message}")
        }
    }

    /**
     * 获取密钥监控概览数据
     * @return 密钥监控概览信息
     */
    @GetMapping("/overview")
    fun getKeyMonitoringOverview(): PlatformResult<KeyMonitoringOverview> {
        log.info { "Getting key monitoring overview" }

        try {
            val overview = keyUsageService.getKeyMonitoringOverview()
            return PlatformResult.success(overview)
        } catch (e: Exception) {
            log.error(e) { "Failed to get key monitoring overview" }
            return PlatformResult.error("Failed to get key monitoring overview: ${e.message}")
        }
    }

    /**
     * 获取所有模型的密钥统计信息
     * @return 所有模型的密钥统计信息
     */
    @GetMapping("/models/stats")
    fun getAllModelsKeyStats(): PlatformResult<Map<String, ModelKeyStats>> {
        log.info { "Getting all models key stats" }

        try {
            val stats = keyUsageService.getAllModelsKeyStats()
            return PlatformResult.success(stats)
        } catch (e: Exception) {
            log.error(e) { "Failed to get all models key stats" }
            return PlatformResult.error("Failed to get all models key stats: ${e.message}")
        }
    }

    /**
     * 获取密钥性能趋势数据
     * @param hours 获取最近多少小时的数据，默认24小时
     * @return 密钥性能趋势数据
     */
    @GetMapping("/performance/trends")
    fun getKeyPerformanceTrends(
        @RequestParam(defaultValue = "24") hours: Int
    ): PlatformResult<List<KeyPerformanceTrend>> {
        log.info { "Getting key performance trends for last $hours hours" }

        try {
            val trends = keyUsageService.getKeyPerformanceTrends(hours)
            return PlatformResult.success(trends)
        } catch (e: Exception) {
            log.error(e) { "Failed to get key performance trends" }
            return PlatformResult.error("Failed to get key performance trends: ${e.message}")
        }
    }
}

/**
 * 调度器配置请求
 */
data class SchedulerConfigRequest(
    val epsilon: Double? = null,
    val errThreshold: Double? = null,
    val windowSize: Int? = null,
    val minSampleSize: Int? = null
)

/**
 * 密钥使用结果请求
 */
data class KeyUsageResultRequest(
    val success: Boolean,
    val needDeleted: Boolean = false
)

/**
 * 密钥监控概览数据
 */
data class KeyMonitoringOverview(
    val totalKeys: Int,
    val activeKeys: Int,
    val disabledKeys: Int,
    val authFailedKeys: Int,
    val totalModels: Int,
    val channelDistribution: Map<String, Int>,
    val avgSuccessRate: Double,
    val totalRequests: Long,
    val lastUpdateTime: String
)

/**
 * 模型密钥统计信息
 */
data class ModelKeyStats(
    val modelName: String,
    val keyCount: Int,
    val avgSuccessRate: Double,
    val totalRequests: Long,
    val bestKeyId: Long?,
    val worstKeyId: Long?,
    val schedulerConfig: SchedulerConfigInfo,
    val keyStats: Map<String, KeyStatInfo>
)

/**
 * 调度器配置信息
 */
data class SchedulerConfigInfo(
    val epsilon: Double,
    val errThreshold: Double,
    val windowSize: Int,
    val minSampleSize: Int
)

/**
 * 密钥统计信息
 */
data class KeyStatInfo(
    val keyId: String,
    val weight: Double,
    val errorRate: Double,
    val score: Double,
    val requestCount: Long,
    val successCount: Long,
    val successRate: Double,
    val channel: String,
    val name: String?
)

/**
 * 密钥性能趋势数据
 */
data class KeyPerformanceTrend(
    val timestamp: String,
    val keyId: Long,
    val modelName: String,
    val successRate: Double,
    val requestCount: Int,
    val avgResponseTime: Double?,
    val errorRate: Double,
    val channel: String
)
