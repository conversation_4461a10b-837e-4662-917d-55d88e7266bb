package io.cliveyou.claudecodeproxybackend.keymanagment.facade.controller

import io.cliveyou.claudecodeproxybackend.common.response.PageResponse
import io.cliveyou.claudecodeproxybackend.common.response.PlatformResult
import io.cliveyou.claudecodeproxybackend.keymanagment.application.service.LlmPriceService
import io.cliveyou.claudecodeproxybackend.keymanagment.facade.dto.*
import kotlinx.coroutines.reactor.mono
import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Mono

/**
 * LLM价格管理控制器
 */
@CrossOrigin
@RestController
@RequestMapping("/api/private/llm-prices")
class LlmPriceController(
    private val llmPriceService: LlmPriceService
) {

    /**
     * 分页查询LLM价格列表
     */
    @PostMapping("/page")
    fun queryPage(@RequestBody request: LlmPriceQueryRequest): Mono<PlatformResult<PageResponse<LlmPriceResponse>>> {
        return mono {
            val pageResponse = llmPriceService.queryPage(request)
            PlatformResult.success(pageResponse)
        }
    }

    /**
     * 创建LLM价格
     */
    @PostMapping
    fun create(@RequestBody request: LlmPriceCreateRequest): Mono<PlatformResult<LlmPriceResponse>> {
        return mono {
            val response = llmPriceService.create(request)
            PlatformResult.success(response)
        }
    }

    /**
     * 获取LLM价格详情
     */
    @GetMapping("/{id}")
    fun getById(@PathVariable id: Long): Mono<PlatformResult<LlmPriceResponse>> {
        return mono {
            val response = llmPriceService.getById(id)
            PlatformResult.success(response)
        }
    }

    /**
     * 更新LLM价格
     */
    @PutMapping("/{id}")
    fun update(
        @PathVariable id: Long,
        @RequestBody request: LlmPriceUpdateRequest
    ): Mono<PlatformResult<LlmPriceResponse>> {
        return mono {
            val response = llmPriceService.update(id, request)
            PlatformResult.success(response)
        }
    }

    /**
     * 删除LLM价格
     */
    @DeleteMapping("/{id}")
    fun delete(@PathVariable id: Long): Mono<PlatformResult<Unit>> {
        return mono {
            llmPriceService.delete(id)
            PlatformResult.success(Unit)
        }
    }

    /**
     * 根据系统模型ID查询价格列表
     */
    @GetMapping("/by-model/{systemModelId}")
    fun getBySystemModelId(@PathVariable systemModelId: Long): Mono<PlatformResult<List<LlmPriceResponse>>> {
        return mono {
            val responses = llmPriceService.getBySystemModelId(systemModelId)
            PlatformResult.success(responses)
        }
    }
}