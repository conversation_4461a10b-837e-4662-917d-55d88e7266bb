package io.cliveyou.claudecodeproxybackend.keymanagment.facade.dto

import io.cliveyou.claudecodeproxybackend.keymanagment.domain.KeyChannel
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.ClaudeType
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.CommonKeyStatus
import java.math.BigDecimal

// 通用请求基类
open class CreateCommonTokenKeyRequest(
    open val accessToken: String?,
    open val domain: String? = null,
    open val type: KeyChannel,
    open val name: String? = null,
    open val supportModels: List<String> = emptyList(),
    open val autoDisable: Boolean = false,
    open val modelMapping: String? = null,
    open val metadata: String? = null,
    open val quota: BigDecimal? = null,
    open val weight: Double = 1.0,
    open val windowSize: Int = 100,
    open val epsilon: Double = 0.05,
    open val errThreshold: Double = 0.5,
    open val minSampleSize: Int = 5
)

// Claude Code 专用创建请求
data class CreateClaudeCode<PERSON>eyRequest(
    override val accessToken: String?,
    override val name: String? = null,
    override val supportModels: List<String> = emptyList(),
    override val autoDisable: Boolean = false,
    override val modelMapping: String? = null,
    override val metadata: String? = null,
    override val weight: Double = 1.0,
    override val windowSize: Int = 100,
    override val epsilon: Double = 0.05,
    override val errThreshold: Double = 0.5,
    override val minSampleSize: Int = 5,
    val refreshToken: String,
    val expiresAt: Long? = null,
    val clientId: String,
    val email: String? = null,
    val accountType: ClaudeType = ClaudeType.PRO
) : CreateCommonTokenKeyRequest(
    accessToken = accessToken,
    type = KeyChannel.ClaudeCode,
    name = name,
    supportModels = supportModels,
    autoDisable = autoDisable,
    modelMapping = modelMapping,
    metadata = metadata,
    quota = null,
    weight = weight,
    windowSize = windowSize,
    epsilon = epsilon,
    errThreshold = errThreshold
)

// 通用更新请求基类
open class UpdateCommonTokenKeyRequest(
    open val id: Long,
    open val accessToken: String? = null,
    open val domain: String? = null,
    open val status: CommonKeyStatus? = null,
    open val name: String? = null,
    open val supportModels: List<String>? = null,
    open val autoDisable: Boolean? = null,
    open val modelMapping: String? = null,
    open val metadata: String? = null,
    open val quota: BigDecimal? = null,
    open val weight: BigDecimal? = null,
    open val windowSize: Int? = null,
    open val epsilon: BigDecimal? = null,
    open val errThreshold: BigDecimal? = null
)

// Claude Code 专用更新请求
data class UpdateClaudeCodeKeyRequest(
    override val id: Long,
    override val accessToken: String? = null,
    override val status: CommonKeyStatus? = null,
    override val name: String? = null,
    override val supportModels: List<String>? = null,
    override val autoDisable: Boolean? = null,
    override val modelMapping: String? = null,
    override val metadata: String? = null,
    override val weight: BigDecimal? = null,
    override val windowSize: Int? = null,
    override val epsilon: BigDecimal? = null,
    override val errThreshold: BigDecimal? = null,
    val refreshToken: String? = null,
    val expiresAt: Long? = null,
    val clientId: String? = null,
    val email: String? = null,
    val accountType: ClaudeType? = null
) : UpdateCommonTokenKeyRequest(
    id = id,
    accessToken = accessToken,
    status = status,
    name = name,
    supportModels = supportModels,
    autoDisable = autoDisable,
    modelMapping = modelMapping,
    metadata = metadata,
    quota = null,
    weight = weight,
    windowSize = windowSize,
    epsilon = epsilon,
    errThreshold = errThreshold
)

// Google Vertex AI 专用更新请求
data class UpdateGoogleVertexAIKeyRequest(
    override val id: Long,
    override val accessToken: String? = null,
    override val status: CommonKeyStatus? = null,
    override val name: String? = null,
    override val supportModels: List<String>? = null,
    override val autoDisable: Boolean? = null,
    override val modelMapping: String? = null,
    override val metadata: String? = null,
    override val quota: BigDecimal? = null,
    override val weight: BigDecimal? = null,
    override val windowSize: Int? = null,
    override val epsilon: BigDecimal? = null,
    override val errThreshold: BigDecimal? = null
) : UpdateCommonTokenKeyRequest(
    id = id,
    accessToken = accessToken,
    status = status,
    name = name,
    supportModels = supportModels,
    autoDisable = autoDisable,
    modelMapping = modelMapping,
    metadata = metadata,
    quota = quota,
    weight = weight,
    windowSize = windowSize,
    epsilon = epsilon,
    errThreshold = errThreshold
)

// 分页查询请求
data class PageCommonTokenKeyRequest(
    val pageNumber: Int,
    val pageSize: Int,
    val name: String? = null,
    val domain: String? = null,
    val status: CommonKeyStatus? = null,
    val type: KeyChannel? = null
)

// Claude Code 刷新信息响应
data class ClaudeCodeRefreshInfoResponse(
    val refreshToken: String,
    val expiresAt: Long? = null,
    val clientId: String,
    val email: String? = null,
    val accountType: ClaudeType? = null,
    val lastRefreshTime: String? = null
)

// Google Vertex AI 服务账户JSON导入请求
data class GoogleServiceAccountJson(
    val type: String,
    val project_id: String,
    val private_key_id: String,
    val private_key: String,
    val client_email: String,
    val client_id: String,
    val auth_uri: String,
    val token_uri: String,
    val auth_provider_x509_cert_url: String,
    val client_x509_cert_url: String,
    val universe_domain: String? = null
)

// Google Vertex AI 密钥创建请求
data class CreateGoogleVertexAIKeyRequest(
    override val name: String? = null,
    override val supportModels: List<String> = emptyList(),
    override val autoDisable: Boolean = false,
    override val modelMapping: String? = null,
    override val metadata: String? = null,
    override val quota: BigDecimal? = null,
    override val weight: Double = 1.0,
    override val windowSize: Int = 100,
    override val epsilon: Double = 0.05,
    override val errThreshold: Double = 0.5,
    val serviceAccountJson: String,  // JSON字符串格式的服务账户
    val projectId: String,
    val clientEmail: String,
    val privateKeyId: String,
    val privateKey: String
) : CreateCommonTokenKeyRequest(
    accessToken = null,
    type = KeyChannel.GoogleVertexAI,
    name = name,
    supportModels = supportModels,
    autoDisable = autoDisable,
    modelMapping = modelMapping,
    metadata = metadata,
    quota = quota,
    weight = weight,
    windowSize = windowSize,
    epsilon = epsilon,
    errThreshold = errThreshold
)

// Google Vertex AI 导入请求
data class ImportGoogleVertexAIKeyRequest(
    val serviceAccountJson: String,  // JSON字符串格式的服务账户
    val name: String? = null,
    val supportModels: List<String> = emptyList(),
    val autoDisable: Boolean = false,
    val modelMapping: String? = null,
    val quota: BigDecimal? = null,
    val weight: Double = 1.0,
    val windowSize: Int = 100,
    val epsilon: Double = 0.05,
    val errThreshold: Double = 0.5,
    val metadata:Map<String, String>? = null
)

// Google Vertex AI 刷新信息响应
data class GoogleVertexAIRefreshInfoResponse(
    val projectId: String,
    val clientEmail: String,
    val privateKeyId: String,
    val expiresAt: Long? = null,
    val lastRefreshTime: String? = null
)

// 通用密钥响应
data class CommonTokenKeyResponse(
    val id: Long,
    val accessToken: String? = null,
    val domain: String? = null,
    val type: KeyChannel,
    val status: CommonKeyStatus,
    val name: String? = null,
    val supportModels: List<String> = emptyList(),
    val autoDisable: Boolean = false,
    val modelMapping: String? = null,
    val metadata: String? = null,
    val quota: BigDecimal? = null,
    val weight: Double = 1.0,
    val windowSize: Int = 100,
    val epsilon: Double = 0.05,
    val errThreshold: Double = 0.5,
    val minSampleSize: Int = 5,
    val createdAt: String,
    val updatedAt: String,
    var claudeCodeRefreshInfo: ClaudeCodeRefreshInfoResponse? = null,
    var googleVertexAIRefreshInfo: GoogleVertexAIRefreshInfoResponse? = null
)

// 批量创建请求
data class BatchCreateCommonTokenKeyRequest(
    val keys: List<CreateCommonTokenKeyRequest>
)

// 批量创建Claude Code请求
data class BatchCreateClaudeCodeKeyRequest(
    val keys: List<CreateClaudeCodeKeyRequest>
)

// 批量创建失败项
data class BatchCreateFailure(
    val index: Int,
    val data: CreateCommonTokenKeyRequest,
    val error: String
)

// 批量创建响应
data class BatchCreateResponse(
    val successful: List<CommonTokenKeyResponse>,
    val failed: List<BatchCreateFailure>
)

// 获取最优密钥请求
data class BestKeyRequest(
    val modelName: String,
    val channels: List<KeyChannel>? = null
)

// 获取最优密钥响应
data class BestKeyResponse(
    val id: Long,
    val token: String,
    val channel: KeyChannel,
    val modelMapping: Map<String, String>? = null,
    val name: String? = null,
    val domain: String? = null,
    val selectedAt: String,
    val metadata:Map<String, String>? = null
)