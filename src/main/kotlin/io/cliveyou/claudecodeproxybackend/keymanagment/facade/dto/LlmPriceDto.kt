package io.cliveyou.claudecodeproxybackend.keymanagment.facade.dto

import java.math.BigDecimal

/**
 * LLM价格查询请求DTO
 */
data class LlmPriceQueryRequest(
    val page: Int = 1,
    val size: Int = 10,
    val systemModelId: Long? = null,
    val type: Int? = null,
    val statuses: Int? = null
)

/**
 * LLM价格创建请求DTO
 */
data class LlmPriceCreateRequest(
    val systemModelId: Long,
    val type: Int,
    val statuses: Int = 1,
    val audioCachePrompt: BigDecimal? = null,
    val audioCompletion: BigDecimal? = null,
    val audioPrompt: BigDecimal? = null,
    val imageCachePrompt: BigDecimal? = null,
    val imageCompletion: BigDecimal? = null,
    val imagePrompt: BigDecimal? = null,
    val reasoningCompletion: BigDecimal? = null,
    val textCachePrompt: BigDecimal? = null,
    val textCachePromptWrite1h: BigDecimal? = null,
    val textCachePromptWrite5m: BigDecimal? = null,
    val textCompletion: BigDecimal? = null,
    val textPrompt: BigDecimal? = null
)

/**
 * LLM价格更新请求DTO
 */
data class LlmPriceUpdateRequest(
    val type: Int? = null,
    val statuses: Int? = null,
    val audioCachePrompt: BigDecimal? = null,
    val audioCompletion: BigDecimal? = null,
    val audioPrompt: BigDecimal? = null,
    val imageCachePrompt: BigDecimal? = null,
    val imageCompletion: BigDecimal? = null,
    val imagePrompt: BigDecimal? = null,
    val reasoningCompletion: BigDecimal? = null,
    val textCachePrompt: BigDecimal? = null,
    val textCachePromptWrite1h: BigDecimal? = null,
    val textCachePromptWrite5m: BigDecimal? = null,
    val textCompletion: BigDecimal? = null,
    val textPrompt: BigDecimal? = null
)

/**
 * LLM价格响应DTO
 */
data class LlmPriceResponse(
    val id: Long,
    val systemModelId: Long,
    val systemModelName: String? = null,
    val type: Int,
    val statuses: Int,
    val createTime: Long? = null,
    val audioCachePrompt: BigDecimal? = null,
    val audioCompletion: BigDecimal? = null,
    val audioPrompt: BigDecimal? = null,
    val imageCachePrompt: BigDecimal? = null,
    val imageCompletion: BigDecimal? = null,
    val imagePrompt: BigDecimal? = null,
    val reasoningCompletion: BigDecimal? = null,
    val textCachePrompt: BigDecimal? = null,
    val textCachePromptWrite1h: BigDecimal? = null,
    val textCachePromptWrite5m: BigDecimal? = null,
    val textCompletion: BigDecimal? = null,
    val textPrompt: BigDecimal? = null
)