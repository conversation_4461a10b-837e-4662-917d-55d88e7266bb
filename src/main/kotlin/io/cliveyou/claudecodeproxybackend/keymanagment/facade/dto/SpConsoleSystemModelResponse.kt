package io.cliveyou.claudecodeproxybackend.keymanagment.facade.dto

import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.SpConsoleSystemModel


/**
 * 系统模型响应DTO
 */
data class SpConsoleSystemModelResponse(
    /**
     * 主键ID
     */
    val id: Long,

    /**
     * 创建时间
     */
    val createTime: Long?,

    /**
     * Logo URL
     */
    val logoUrl: String?,


    /**
     * 模型名称
     */
    val modelName: String?,

    /**
     * 模型类型
     */
    val modelType: Int?,


    /**
     * 状态
     */
    val statuses: Int?,

    val manufacturerName: String?,

    val vendorNames: List<String>,

    )

/**
 * 将Entity转换为Response DTO
 */
fun SpConsoleSystemModel.toResponse(): SpConsoleSystemModelResponse {
    return SpConsoleSystemModelResponse(
        id = this.id!!,
        createTime = this.createTime,
        logoUrl = this.logoUrl,
        modelName = this.modelName,
        modelType = this.modelType,
        statuses = this.statuses,
        manufacturerName = this.manufacturerName,
        vendorNames = this.vendorNames
    )
}
