package io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Table
import java.time.LocalDateTime

@Entity
@Table(name = " sp_platform_claude_code_token_refresh_info")
data class ClaudeCodeTokenRefreshInfo(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long? = null,

    /**
     * 关联的 CommonTokenKey ID
     */
    @Column(name = "common_key_id", nullable = false, unique = true)
    val commonKeyId: Long,

    /**
     * Claude OAuth刷新令牌
     */
    @Column(name = "refresh_token", columnDefinition = "TEXT", nullable = false)
    val refreshToken: String,

    /**
     * 令牌过期时间戳（秒）
     */
    @Column(name = "expires_at")
    val expiresAt: Long? = null,

    /**
     * Claude客户端ID
     */
    @Column(name = "client_id", nullable = false)
    val clientId: String,

    /**
     * 关联的邮箱地址
     */
    @Column(name = "email")
    val email: String? = null,

    /**
     * Claude账户类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "claude_account_type")
    val accountType: ClaudeType? = null,

    /**
     * 上次刷新时间
     */
    @Column(name = "last_refresh_time")
    val lastRefreshTime: LocalDateTime? = null,

    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    val updatedAt: LocalDateTime = LocalDateTime.now()
)


/**
 * Claude账户类型枚举
 */
enum class ClaudeType {
    PRO,
    MAX_100,
    MAX_200
}