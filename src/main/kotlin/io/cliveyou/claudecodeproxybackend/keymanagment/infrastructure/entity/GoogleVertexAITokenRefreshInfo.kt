package io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Table
import java.time.LocalDateTime

@Entity
@Table(name = " sp_platform_google_vertex_ai_token_refresh_info")
data class GoogleVertexAITokenRefreshInfo(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long? = null,

    /**
     * 关联的 CommonTokenKey ID
     */
    @Column(name = "common_key_id", nullable = false, unique = true)
    val commonKeyId: Long,

    /**
     * Google Cloud 项目ID
     */
    @Column(name = "project_id", nullable = false)
    val projectId: String,

    /**
     * 服务账户邮箱
     */
    @Column(name = "client_email", nullable = false)
    val clientEmail: String,

    /**
     * 服务账户私钥ID
     */
    @Column(name = "private_key_id", nullable = false)
    val privateKeyId: String,

    /**
     * 服务账户私钥（RSA格式）
     */
    @Column(name = "private_key", columnDefinition = "TEXT", nullable = false)
    val privateKey: String,

    /**
     * 令牌发行者URL
     */
    @Column(name = "token_uri")
    val tokenUri: String = "https://oauth2.googleapis.com/token",

    /**
     * API访问范围
     */
    @Column(name = "scope")
    val scope: String = "https://www.googleapis.com/auth/cloud-platform",

    /**
     * 令牌过期时间戳（秒）
     */
    @Column(name = "expires_at")
    val expiresAt: Long? = null,

    /**
     * 上次刷新时间
     */
    @Column(name = "last_refresh_time")
    val lastRefreshTime: LocalDateTime? = null,

    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    val updatedAt: LocalDateTime = LocalDateTime.now()
)