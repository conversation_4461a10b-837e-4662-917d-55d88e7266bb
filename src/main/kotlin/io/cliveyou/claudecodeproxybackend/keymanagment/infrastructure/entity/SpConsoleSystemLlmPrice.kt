package io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity

import jakarta.persistence.*
import java.math.BigDecimal

/**
 * LLM价格实体类
 * 对应数据库表 sp_console_system_llm_price
 */
@Entity
@Table(name = "sp_platform_system_llm_price")
data class SpConsoleSystemLlmPrice(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long? = null,

    /**
     * 音频缓存提示价格
     */
    @Column(name = "audio_cache_prompt", precision = 29, scale = 18)
    val audioCachePrompt: BigDecimal? = null,

    /**
     * 音频完成价格
     */
    @Column(name = "audio_completion", precision = 29, scale = 18)
    val audioCompletion: BigDecimal? = null,

    /**
     * 音频提示价格
     */
    @Column(name = "audio_prompt", precision = 29, scale = 18)
    val audioPrompt: BigDecimal? = null,

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    val createTime: Long? = null,

    /**
     * 图像缓存提示价格
     */
    @Column(name = "image_cache_prompt", precision = 29, scale = 18)
    val imageCachePrompt: BigDecimal? = null,

    /**
     * 图像完成价格
     */
    @Column(name = "image_completion", precision = 29, scale = 18)
    val imageCompletion: BigDecimal? = null,

    /**
     * 图像提示价格
     */
    @Column(name = "image_prompt", precision = 29, scale = 18)
    val imagePrompt: BigDecimal? = null,

    /**
     * 推理完成价格
     */
    @Column(name = "reasoning_completion", precision = 29, scale = 18)
    val reasoningCompletion: BigDecimal? = null,

    /**
     * 状态
     */
    @Column(name = "statuses")
    val statuses: Int? = null,

    /**
     * 系统模型ID
     */
    @Column(name = "system_model_id")
    val systemModelId: Long? = null,

    /**
     * 文本缓存提示价格
     */
    @Column(name = "text_cache_prompt", precision = 29, scale = 18)
    val textCachePrompt: BigDecimal? = null,

    /**
     * 文本缓存提示写入1小时价格
     */
    @Column(name = "text_cache_prompt_write1h", precision = 29, scale = 18)
    val textCachePromptWrite1h: BigDecimal? = null,

    /**
     * 文本缓存提示写入5分钟价格
     */
    @Column(name = "text_cache_prompt_write5m", precision = 29, scale = 18)
    val textCachePromptWrite5m: BigDecimal? = null,

    /**
     * 文本完成价格
     */
    @Column(name = "text_completion", precision = 29, scale = 18)
    val textCompletion: BigDecimal? = null,

    /**
     * 文本提示价格
     */
    @Column(name = "text_prompt", precision = 29, scale = 18)
    val textPrompt: BigDecimal? = null,

    /**
     * 类型
     */
    @Column(name = "type")
    val type: Int? = null,
)