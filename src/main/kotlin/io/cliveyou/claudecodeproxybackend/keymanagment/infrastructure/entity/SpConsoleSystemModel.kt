package io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity

import io.hypersistence.utils.hibernate.type.array.ListArrayType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Table
import org.hibernate.annotations.ColumnDefault
import org.hibernate.annotations.Type

/**
 * 系统模型实体类
 * 对应数据库表 sp_console_system_model
 */
@Entity
@Table(name = "sp_platform_system_model_info")
data class SpConsoleSystemModel(

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long? = null,

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    val createTime: Long? = null,

    /**
     * Logo URL
     */
    @Column(name = "logo_url", columnDefinition = "TEXT")
    val logoUrl: String? = null,


    val manufacturerName: String? = null,

    /**
     * 厂商名称
     */
    @ColumnDefault("'{}'")
    @Type(ListArrayType::class)
    @Column(nullable = false, columnDefinition = "text[]")
    val vendorNames: List<String> = emptyList(),

    /**
     * 模型名称
     */
    @Column(name = "model_name")
    val modelName: String? = null,

    /**
     * 模型类型: 0 llm 1 search
     */
    @Column(name = "model_type")
    val modelType: Int? = null,


    /**
     * 状态
     */
    @Column(name = "statuses")
    val statuses: Int? = null,

    )