package io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.remote.flush

import com.fasterxml.jackson.annotation.JsonProperty
import io.cliveyou.claudecodeproxybackend.common.exception.TokenRefreshException
import io.cliveyou.claudecodeproxybackend.common.exception.ExternalServiceException
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.CommonTokenKey
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.GoogleVertexAITokenRefreshInfo
import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.springframework.stereotype.Service
import java.net.URI
import java.net.URLEncoder
import java.net.http.HttpClient
import java.net.http.HttpRequest
import java.net.http.HttpResponse
import java.nio.charset.StandardCharsets
import java.security.KeyFactory
import java.security.PrivateKey
import java.security.Signature
import java.security.spec.PKCS8EncodedKeySpec
import java.time.Duration
import java.time.Instant
import java.util.Base64

@Service
class GoogleVertexAIFlushService(
    private val objectMapper: ObjectMapper
) {

    private val logger = KotlinLogging.logger {}

    private val httpClient = HttpClient.newBuilder()
        .connectTimeout(Duration.ofSeconds(30))
        .build()

    /**
     * 刷新Google Vertex AI的访问令牌
     * 使用服务账户私钥生成JWT并交换访问令牌
     */
    suspend fun refreshAccessToken(refreshInfo: GoogleVertexAITokenRefreshInfo): GoogleTokenResponse? {
        return withContext(Dispatchers.IO) {
            try {
                // 创建JWT
                val jwt = createJWT(
                    clientEmail = refreshInfo.clientEmail,
                    privateKey = refreshInfo.privateKey,
                    tokenUri = refreshInfo.tokenUri,
                    scope = refreshInfo.scope
                )

                // 构建表单数据
                val formData = mapOf(
                    "grant_type" to "urn:ietf:params:oauth:grant-type:jwt-bearer",
                    "assertion" to jwt
                )

                val requestBody = formData.entries.joinToString("&") { (key, value) ->
                    "${URLEncoder.encode(key, StandardCharsets.UTF_8)}=${URLEncoder.encode(value, StandardCharsets.UTF_8)}"
                }

                val httpRequest = HttpRequest.newBuilder()
                    .uri(URI.create(refreshInfo.tokenUri))
                    .header("Content-Type", "application/x-www-form-urlencoded")
                    .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                    .build()

                logger.debug { "发送Google Vertex AI token刷新请求: clientEmail=${refreshInfo.clientEmail}" }

                val response = httpClient.send(httpRequest, HttpResponse.BodyHandlers.ofString())
                val content = response.body()

                if (response.statusCode() == 200) {
                    val tokenResponse = objectMapper.readValue<GoogleTokenResponse>(content)
                    logger.info { "Google Vertex AI token刷新成功: expiresIn=${tokenResponse.expiresIn}" }
                    tokenResponse
                } else {
                    logger.error { "Google Vertex AI token刷新失败: status=${response.statusCode()}, body=$content" }
                    // 解析错误响应
                    try {
                        val errorResponse = objectMapper.readValue<Map<String, Any>>(content)
                        val error = errorResponse["error"] as? String ?: "unknown_error"
                        val errorDescription = errorResponse["error_description"] as? String ?: "未知错误"
                        when (error) {
                            "invalid_grant" -> throw TokenRefreshException("服务账户无效或已禁用: $errorDescription")
                            else -> throw TokenRefreshException("Token刷新失败: $errorDescription")
                        }
                    } catch (e: Exception) {
                        if (e is TokenRefreshException) throw e
                        throw ExternalServiceException("Token刷新失败: HTTP ${response.statusCode()}")
                    }
                }
            } catch (e: Exception) {
                logger.error(e) { "Google Vertex AI token刷新异常: clientEmail=${refreshInfo.clientEmail}" }
                throw e
            }
        }
    }

    /**
     * 创建JWT用于服务账户认证
     */
    private fun createJWT(
        clientEmail: String,
        privateKey: String,
        tokenUri: String,
        scope: String
    ): String {
        val now = Instant.now()
        val expiry = now.plusSeconds(3600) // JWT有效期1小时

        // JWT Header
        val header = mapOf(
            "alg" to "RS256",
            "typ" to "JWT"
        )

        // JWT Claims
        val claims = mapOf(
            "iss" to clientEmail,
            "sub" to clientEmail,
            "aud" to tokenUri,
            "iat" to now.epochSecond,
            "exp" to expiry.epochSecond,
            "scope" to scope
        )

        // 编码Header和Claims
        val encodedHeader = Base64.getUrlEncoder().withoutPadding()
            .encodeToString(objectMapper.writeValueAsBytes(header))
        val encodedClaims = Base64.getUrlEncoder().withoutPadding()
            .encodeToString(objectMapper.writeValueAsBytes(claims))

        val unsignedJWT = "$encodedHeader.$encodedClaims"

        // 签名JWT
        val signature = signJWT(unsignedJWT, privateKey)
        
        return "$unsignedJWT.$signature"
    }

    /**
     * 使用私钥签名JWT
     */
    private fun signJWT(data: String, privateKeyPem: String): String {
        // 清理私钥PEM格式
        val privateKeyContent = privateKeyPem
            .replace("-----BEGIN PRIVATE KEY-----", "")
            .replace("-----END PRIVATE KEY-----", "")
            .replace("\\n", "")
            .replace("\n", "")
            .trim()

        val keyBytes = Base64.getDecoder().decode(privateKeyContent)
        val keySpec = PKCS8EncodedKeySpec(keyBytes)
        val keyFactory = KeyFactory.getInstance("RSA")
        val privateKey = keyFactory.generatePrivate(keySpec)

        val signature = Signature.getInstance("SHA256withRSA")
        signature.initSign(privateKey)
        signature.update(data.toByteArray(StandardCharsets.UTF_8))

        return Base64.getUrlEncoder().withoutPadding()
            .encodeToString(signature.sign())
    }

    /**
     * 刷新Google Vertex AI的token
     * @param tokenKey 通用密钥
     * @param refreshInfo 刷新信息
     * @return 刷新结果，包含新的accessToken和expiresAt
     */
    suspend fun refreshGoogleVertexAIToken(
        tokenKey: CommonTokenKey,
        refreshInfo: GoogleVertexAITokenRefreshInfo
    ): GoogleVertexAIRefreshResult? {
        return try {
            log.info { "Refresh Token | Start refresh for key ${tokenKey.id} | Info: Attempting token refresh" }

            val tokenResponse = refreshAccessToken(refreshInfo)

            if (tokenResponse != null) {
                val result = GoogleVertexAIRefreshResult(
                    keyId = tokenKey.id!!,
                    accessToken = tokenResponse.accessToken,
                    expiresAt = calculateExpirationTimestamp(tokenResponse.expiresIn)
                )

                log.info { "Refresh Token | Token refresh for key ${tokenKey.id} | Success: New expiration at ${result.expiresAt}" }
                result
            } else {
                log.error { "Refresh Token | Token refresh for key ${tokenKey.id} | Failed: Token response is null | Suggestion: Check service account validity" }
                null
            }
        } catch (e: Exception) {
            log.error(e) { "Refresh Token | Token refresh for key ${tokenKey.id} | Failed: ${e.message} | Suggestion: Check network connectivity and service account validity" }
            throw e
        }
    }

    /**
     * 批量刷新Google Vertex AI token
     * @param pairs 密钥和刷新信息的配对列表
     * @return 刷新结果
     */
    suspend fun refreshGoogleVertexAITokens(
        pairs: List<Pair<CommonTokenKey, GoogleVertexAITokenRefreshInfo>>
    ): GoogleVertexAITokenRefreshBatchResult {
        val successfulResults = mutableListOf<GoogleVertexAIRefreshResult>()
        val failedPairs = mutableListOf<Pair<CommonTokenKey, GoogleVertexAITokenRefreshInfo>>()

        log.info { "Refresh Token | Batch refresh started | Info: Processing ${pairs.size} keys" }

        for ((tokenKey, refreshInfo) in pairs) {
            val result = refreshGoogleVertexAIToken(tokenKey, refreshInfo)
            if (result != null) {
                successfulResults.add(result)
            } else {
                failedPairs.add(tokenKey to refreshInfo)
            }
        }

        val result = GoogleVertexAITokenRefreshBatchResult(
            successfulResults = successfulResults,
            failedPairs = failedPairs,
            totalProcessed = pairs.size
        )

        log.info { "Refresh Token | Batch refresh completed | Success: ${successfulResults.size}/${pairs.size} keys refreshed successfully" }

        return result
    }

    /**
     * 计算token过期时间戳
     */
    fun calculateExpirationTimestamp(expiresIn: Int): Long {
        return System.currentTimeMillis() / 1000 + expiresIn
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}

/**
 * Google Token响应DTO
 */
data class GoogleTokenResponse(
    @JsonProperty("access_token")
    val accessToken: String,
    @JsonProperty("expires_in")
    val expiresIn: Int,
    @JsonProperty("token_type")
    val tokenType: String
)

/**
 * Google Vertex AI刷新结果
 */
data class GoogleVertexAIRefreshResult(
    val keyId: Long,
    val accessToken: String,
    val expiresAt: Long
)

/**
 * 批量刷新结果
 */
data class GoogleVertexAITokenRefreshBatchResult(
    val successfulResults: List<GoogleVertexAIRefreshResult>,
    val failedPairs: List<Pair<CommonTokenKey, GoogleVertexAITokenRefreshInfo>>,
    val totalProcessed: Int
)