package io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.remote.test

import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service
import org.springframework.web.reactive.function.BodyInserters
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.WebClientResponseException
import reactor.core.publisher.Mono

@Service
class ClaudeTestRemoteService(
    private val webClient: WebClient,
) {

    fun testKey(key: String, model: String): Mono<ApiTestResult> {
        val body = """
            {
                "model": "$model",
                "stream": false,
                "system": [
                    {
                        "type": "text",
                        "text": "You are <PERSON> Code, <PERSON>throp<PERSON>'s official CLI for Claude."
                    }
                ],
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "reply1"
                            }
                        ]
                    }
                ],
                "max_tokens": 5
            }
        """.trimIndent()
        return webClient.post().uri("https://api.anthropic.com/v1/messages")
            .let {
                setClaudeHeaders(it, key)
            }
            .body(BodyInserters.fromValue(body))
            .retrieve()
            .bodyToMono(String::class.java)
            .map {
                ApiTestResult(200, it)
            }.onErrorResume {
                if (it is WebClientResponseException) {
                   Mono.just( ApiTestResult(it.statusCode.value(), it.responseBodyAsString))
                } else {
                    Mono.just(ApiTestResult(1000, it.message))
                }
            }
    }


    /**
     * 设置Claude API请求头
     */
    private fun setClaudeHeaders(spec: WebClient.RequestBodySpec, token: String): WebClient.RequestBodySpec {
        return spec
            .header("authorization", "Bearer $token")
            .header("anthropic-version", "2023-06-01")
            .header("content-type", "application/json")
            .header("x-app", "cli")
            .header("User-Agent", "claude-cli/1.0.41 (external, cli)")
            .header(
                "anthropic-beta",
                "claude-code-20250219,oauth-2025-04-20,interleaved-thinking-2025-05-14,fine-grained-tool-streaming-2025-05-14"
            )
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}

data class ApiTestResult(val statusCode: Int, val body: String?)