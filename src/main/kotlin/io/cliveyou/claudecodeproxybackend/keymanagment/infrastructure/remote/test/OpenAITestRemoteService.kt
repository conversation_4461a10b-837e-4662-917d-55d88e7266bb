package io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.remote.test

import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service
import org.springframework.web.reactive.function.BodyInserters
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.WebClientResponseException
import reactor.core.publisher.Mono

@Service
class OpenAITestRemoteService(
    private val webClient: WebClient,
) {

    fun testKey(key: String, model: String, domain: String?): Mono<ApiTestResult> {
        val realDomain = domain ?: "https://api.openai.com"
        val body = """
            {
                "model": "$model",
                "messages": [
                    {
                        "role": "user",
                        "content": "reply1"
                    }
                ],
                "max_tokens": 5,
                "stream": false
            }
        """.trimIndent()
        return webClient.post().uri("$realDomain/v1/chat/completions")
            .let {
                setOpenAIHeaders(it, key)
            }
            .body(BodyInserters.fromValue(body))
            .retrieve()
            .bodyToMono(String::class.java)
            .map {
                ApiTestResult(200, it)
            }.onErrorResume {
                if (it is WebClientResponseException) {
                    Mono.just(ApiTestResult(it.statusCode.value(), it.responseBodyAsString))
                } else {
                    Mono.just(ApiTestResult(1000, it.message))
                }
            }
    }


    /**
     * 设置OpenAI API请求头
     */
    private fun setOpenAIHeaders(spec: WebClient.RequestBodySpec, token: String): WebClient.RequestBodySpec {
        return spec
            .header("Authorization", "Bearer $token")
            .header("Content-Type", "application/json")
            .header("User-Agent", "claude-cli/1.0.41 (external, cli)")
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}