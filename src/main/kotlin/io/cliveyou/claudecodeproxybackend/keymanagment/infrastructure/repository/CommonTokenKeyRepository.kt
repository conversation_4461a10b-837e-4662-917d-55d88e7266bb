package io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.repository

import io.cliveyou.claudecodeproxybackend.keymanagment.domain.KeyChannel
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.CommonKeyStatus
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.CommonTokenKey
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.stereotype.Repository

@Repository
interface CommonTokenKeyRepository : JpaRepository<CommonTokenKey, Long>, JpaSpecificationExecutor<CommonTokenKey> {

    /**
     * 查找所有指定状态的密钥
     * @param status 密钥状态
     * @return 匹配的密钥列表
     */
    fun findByStatus(status: CommonKeyStatus): List<CommonTokenKey>

    /**
     * 根据状态和类型查询密钥
     * @param status 密钥状态
     * @param type 渠道类型
     * @return 匹配的密钥列表
     */
    fun findByStatusAndType(status: CommonKeyStatus, type: KeyChannel): List<CommonTokenKey>


}