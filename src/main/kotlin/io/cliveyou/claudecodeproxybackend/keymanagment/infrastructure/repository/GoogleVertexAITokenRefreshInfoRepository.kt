package io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.repository

import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.GoogleVertexAITokenRefreshInfo
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.util.Optional

@Repository
interface GoogleVertexAITokenRefreshInfoRepository : JpaRepository<GoogleVertexAITokenRefreshInfo, Long> {

    /**
     * 根据 CommonTokenKey ID 查找刷新信息
     * @param commonKeyId CommonTokenKey 的 ID
     * @return 刷新信息
     */
    fun findByCommonKeyId(commonKeyId: Long): Optional<GoogleVertexAITokenRefreshInfo>

    /**
     * 查找即将在指定时间内过期的刷新信息
     * @param expirationThreshold 过期时间阈值（时间戳秒）
     * @return 即将过期的刷新信息列表
     */
    @Query("""
        SELECT r FROM GoogleVertexAITokenRefreshInfo r 
        WHERE r.expiresAt IS NOT NULL 
        AND r.expiresAt <= :expirationThreshold
    """)
    fun findRefreshInfoExpiringBefore(
        @Param("expirationThreshold") expirationThreshold: Long
    ): List<GoogleVertexAITokenRefreshInfo>

    /**
     * 根据项目ID查找刷新信息
     * @param projectId Google Cloud项目ID
     * @return 刷新信息列表
     */
    fun findByProjectId(projectId: String): List<GoogleVertexAITokenRefreshInfo>

    /**
     * 根据服务账户邮箱查找刷新信息
     * @param clientEmail 服务账户邮箱
     * @return 刷新信息列表
     */
    fun findByClientEmail(clientEmail: String): List<GoogleVertexAITokenRefreshInfo>

    /**
     * 删除指定 CommonTokenKey ID 的刷新信息
     * @param commonKeyId CommonTokenKey 的 ID
     */
    fun deleteByCommonKeyId(commonKeyId: Long)

    /**
     * 检查指定 CommonTokenKey ID 是否存在刷新信息
     * @param commonKeyId CommonTokenKey 的 ID
     * @return 是否存在
     */
    fun existsByCommonKeyId(commonKeyId: Long): Boolean
}