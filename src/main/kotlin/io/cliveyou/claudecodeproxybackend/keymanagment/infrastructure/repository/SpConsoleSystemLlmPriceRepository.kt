package io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.repository

import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.SpConsoleSystemLlmPrice
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

/**
 * LLM价格Repository接口
 */
@Repository
interface SpConsoleSystemLlmPriceRepository : JpaRepository<SpConsoleSystemLlmPrice, Long> {

    /**
     * 根据系统模型ID查询价格列表
     */
    fun findBySystemModelId(systemModelId: Long): List<SpConsoleSystemLlmPrice>

    /**
     * 根据系统模型ID和类型查询价格
     */
    fun findBySystemModelIdAndType(systemModelId: Long, type: Int): SpConsoleSystemLlmPrice?

    /**
     * 分页查询价格列表，支持多条件筛选
     */
    @Query("""
        SELECT p FROM SpConsoleSystemLlmPrice p
        WHERE (:systemModelId IS NULL OR p.systemModelId = :systemModelId)
          AND (:type IS NULL OR p.type = :type)
          AND (:statuses IS NULL OR p.statuses = :statuses)
        ORDER BY p.createTime DESC
    """)
    fun findByConditions(
        @Param("systemModelId") systemModelId: Long?,
        @Param("type") type: Int?,
        @Param("statuses") statuses: Int?,
        pageable: Pageable
    ): Page<SpConsoleSystemLlmPrice>

    /**
     * 检查是否存在特定模型和类型的价格配置
     */
    fun existsBySystemModelIdAndType(systemModelId: Long, type: Int): Boolean

    /**
     * 根据状态查询价格列表
     */
    fun findByStatuses(statuses: Int): List<SpConsoleSystemLlmPrice>

    /**
     * 根据类型查询价格列表
     */
    fun findByType(type: Int): List<SpConsoleSystemLlmPrice>
}