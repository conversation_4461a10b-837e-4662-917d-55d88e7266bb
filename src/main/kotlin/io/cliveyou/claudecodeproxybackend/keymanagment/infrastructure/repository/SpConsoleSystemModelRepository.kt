package io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.repository

import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.SpConsoleSystemModel
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

/**
 * 系统模型数据访问层
 */
@Repository
interface SpConsoleSystemModelRepository : JpaRepository<SpConsoleSystemModel, Long>,
    JpaSpecificationExecutor<SpConsoleSystemModel> {


    /**
     * 根据厂商名称查询模型
     * @param manufacturerName 厂商名称
     * @return 匹配的模型列表
     */
    fun findByManufacturerNameContainingIgnoreCase(manufacturerName: String): List<SpConsoleSystemModel>

    /**
     * 根据模型名称查询
     * @param modelName 模型名称
     * @return 匹配的模型列表
     */
    fun findByModelNameContainingIgnoreCase(modelName: String): List<SpConsoleSystemModel>

    /**
     * 根据模型类型查询
     * @param modelType 模型类型
     * @return 匹配的模型列表
     */
    fun findByModelType(modelType: Int): List<SpConsoleSystemModel>

    /**
     * 根据状态查询
     * @param statuses 状态
     * @return 匹配的模型列表
     */
    fun findByStatuses(statuses: Int): List<SpConsoleSystemModel>

    /**
     * 查询所有有效的模型（显示且未消失）
     * @param currentTime 当前时间戳
     * @return 有效的模型列表
     */
    @Query("""
        SELECT m FROM SpConsoleSystemModel m 
        WHERE m.statuses =1
    """)
    fun findAllActiveModels(@Param("currentTime") currentTime: Long): List<SpConsoleSystemModel>

    /**
     * 根据厂商名称和模型类型查询
     * @param manufacturerName 厂商名称
     * @param modelType 模型类型
     * @return 匹配的模型列表
     */
    fun findByManufacturerNameAndModelType(manufacturerName: String, modelType: Int): List<SpConsoleSystemModel>
}