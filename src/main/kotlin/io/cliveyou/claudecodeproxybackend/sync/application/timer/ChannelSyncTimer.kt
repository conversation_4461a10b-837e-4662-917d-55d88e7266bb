package io.cliveyou.claudecodeproxybackend.sync.application.timer

import io.cliveyou.claudecodeproxybackend.azure.domain.AzureChannelSyncService
import io.cliveyou.claudecodeproxybackend.sync.domain.ChannelSyncService
import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.coroutines.runBlocking
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

/**
 * Channel同步定时任务
 * 定期从GCP MySQL同步Google Vertex AI密钥数据
 * 定期从Azure MySQL同步OpenAI密钥数据
 */
@Component
class ChannelSyncTimer(
    private val channelSyncService: ChannelSyncService,
    private val azureChannelSyncService: AzureChannelSyncService
) {
    
    private val log = KotlinLogging.logger {}

    /**
     * 定时同步Google Vertex AI密钥
     * 每30分钟执行一次增量同步
     */
//    @Scheduled(fixedRate = 1800000) // 30分钟 = 1,800,000毫秒
    fun syncGoogleVertexAIChannels() {
        runBlocking {
            try {
                log.info { "Channel Sync Timer | Scheduled sync started | Info: Starting Google Vertex AI channel sync task" }
                
                val result = channelSyncService.performIncrementalSync()
                
                if (result.success) {
                    log.info { 
                        "Channel Sync Timer | Scheduled sync completed successfully | " +
                        "Success: Synced ${result.syncCount} channels | " +
                        "Duration: ${result.duration}ms | " +
                        "Message: ${result.message}"
                    }
                } else {
                    log.error { 
                        "Channel Sync Timer | Scheduled sync failed | " +
                        "Failed: ${result.message} | " +
                        "Duration: ${result.duration}ms"
                    }
                }
                
            } catch (e: Exception) {
                log.error(e) { 
                    "Channel Sync Timer | Scheduled sync encountered unexpected error | " +
                    "Failed: ${e.message} | " +
                    "Suggestion: Check database connectivity and service configuration"
                }
            }
        }
    }

    /**
     * 定时同步Azure OpenAI密钥
     * 每30分钟执行一次增量同步
     */
    @Scheduled(fixedRate = 1800000) // 30分钟 = 1,800,000毫秒
    fun syncAzureOpenAIChannels() {
        runBlocking {
            try {
                log.info { "Azure Channel Sync Timer | Scheduled sync started | Info: Starting Azure OpenAI channel sync task" }

                val result = azureChannelSyncService.performIncrementalSync()

                if (result.success) {
                    log.info {
                        "Azure Channel Sync Timer | Scheduled sync completed successfully | " +
                        "Success: Synced ${result.syncCount} channels | " +
                        "Duration: ${result.duration}ms | " +
                        "Message: ${result.message}"
                    }
                } else {
                    log.error {
                        "Azure Channel Sync Timer | Scheduled sync failed | " +
                        "Failed: ${result.message} | " +
                        "Duration: ${result.duration}ms"
                    }
                }

            } catch (e: Exception) {
                log.error(e) {
                    "Azure Channel Sync Timer | Scheduled sync encountered unexpected error | " +
                    "Failed: ${e.message} | " +
                    "Suggestion: Check database connectivity and service configuration"
                }
            }
        }
    }

    /**
     * 手动触发Google Vertex AI同步（用于测试或紧急同步）
     * 可以通过JMX或其他方式调用
     */
    fun triggerManualSync(): ChannelSyncService.SyncResult {
        return runBlocking {
            try {
                log.info { "Channel Sync Timer | Manual sync triggered | Info: Starting manual Google Vertex AI channel sync" }
                
                val result = channelSyncService.performIncrementalSync()
                
                log.info { 
                    "Channel Sync Timer | Manual sync completed | " +
                    "Success: ${result.success} | " +
                    "Synced: ${result.syncCount} channels | " +
                    "Duration: ${result.duration}ms"
                }
                
                result
                
            } catch (e: Exception) {
                log.error(e) { "Channel Sync Timer | Manual sync failed | Failed: ${e.message}" }
                ChannelSyncService.SyncResult(
                    success = false,
                    syncCount = 0,
                    message = "Manual sync failed: ${e.message}",
                    duration = 0L
                )
            }
        }
    }

    /**
     * 手动触发Azure OpenAI同步（用于测试或紧急同步）
     * 可以通过JMX或其他方式调用
     */
    fun triggerManualAzureSync(): AzureChannelSyncService.SyncResult {
        return runBlocking {
            try {
                log.info { "Azure Channel Sync Timer | Manual sync triggered | Info: Starting manual Azure OpenAI channel sync" }

                val result = azureChannelSyncService.performIncrementalSync()

                log.info {
                    "Azure Channel Sync Timer | Manual sync completed | " +
                    "Success: ${result.success} | " +
                    "Synced: ${result.syncCount} channels | " +
                    "Duration: ${result.duration}ms"
                }

                result

            } catch (e: Exception) {
                log.error(e) { "Azure Channel Sync Timer | Manual sync failed | Failed: ${e.message}" }
                AzureChannelSyncService.SyncResult(
                    success = false,
                    syncCount = 0,
                    message = "Manual sync failed: ${e.message}",
                    duration = 0L
                )
            }
        }
    }
}
