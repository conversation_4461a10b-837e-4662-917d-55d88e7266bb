spring:
  datasource:
    pg:
      jdbc-url: ************************************************************************
      username: ${DB_USERNAME:hispread}
      password: ${DB_PASSWORD:Hispread0719.}
      driver-class-name: org.postgresql.Driver
    mysql:
      jdbc-url: ***********************************************************************************************
      username: ${MYSQL_USERNAME:gcp}
      password: ${MYSQL_PASSWORD:5BJnDPRLZ3zSDekw}
      driver-class-name: com.mysql.cj.jdbc.Driver
    azure:
      jdbc-url: *****************************************************************************************************
      username: ${AZURE_USERNAME:root}
      password: ${AZURE_PASSWORD:yeqiu669.}
      driver-class-name: com.mysql.cj.jdbc.Driver


  jpa:
    pg:
      properties:
        hibernate:
          ddl-auto: none
          dialect: org.hibernate.dialect.PostgreSQLDialect
    mysql:
      properties:
        hibernate:
          ddl-auto: none
          dialect: org.hibernate.dialect.MySQLDialect
    azure:
      properties:
        hibernate:
          ddl-auto: none
          dialect: org.hibernate.dialect.MySQLDialect

#  sql:
#    init:
#      mode: always
#      platform: postgresql

  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      file-size-threshold: 2KB
      enabled: true
      resolve-lazily: false

server:
  port: 80
  tomcat:
    max-part-count: 100
