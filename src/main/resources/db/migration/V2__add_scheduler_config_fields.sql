-- 添加密钥调度器配置字段到 common_code_key 表

-- 添加权重字段
ALTER TABLE common_code_key 
ADD COLUMN weight DECIMAL(5,2) NOT NULL DEFAULT 1.0 
COMMENT '密钥权重，用于调度器选择密钥时的权重计算';

-- 添加滑动窗口大小字段
ALTER TABLE common_code_key 
ADD COLUMN window_size INTEGER NOT NULL DEFAULT 100 
COMMENT '滑动窗口大小，用于统计错误率';

-- 添加探索率字段
ALTER TABLE common_code_key 
ADD COLUMN epsilon DECIMAL(5,3) NOT NULL DEFAULT 0.05 
COMMENT '探索率 ε，控制随机选择的概率';

-- 添加错误阈值字段
ALTER TABLE common_code_key 
ADD COLUMN err_threshold DECIMAL(5,3) NOT NULL DEFAULT 0.5 
COMMENT '错误阈值，超过此值的密钥将被过滤';

-- 创建索引以提高查询性能
CREATE INDEX idx_common_code_key_weight ON common_code_key(weight);
CREATE INDEX idx_common_code_key_epsilon ON common_code_key(epsilon);
CREATE INDEX idx_common_code_key_err_threshold ON common_code_key(err_threshold);
