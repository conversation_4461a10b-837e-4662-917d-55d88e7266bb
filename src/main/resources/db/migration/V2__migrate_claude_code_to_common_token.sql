-- 迁移 Claude Code Key 数据到新的表结构

-- 1. 首先将 claude_code_key 的数据迁移到 common_code_key
INSERT INTO common_code_key (
    access_token,
    domain,
    type,
    status,
    name,
    support_models,
    created_at,
    updated_at
)
SELECT 
    access_token,
    NULL as domain,
    '<PERSON><PERSON>ode' as type,
    CASE 
        WHEN status = 'ACTIVE' THEN 'ACTIVE'
        WHEN status = 'DISABLED' THEN 'DISABLED'
        WHEN status = 'AUTH_FAILED' THEN 'AUTH_FAILED'
        ELSE 'DISABLED'
    END as status,
    name,
    support_models,
    created_at,
    updated_at
FROM claude_code_key;

-- 2. 创建 claude_code_token_refresh_info 数据
-- 需要先获取刚插入的 common_code_key 的 ID
WITH key_mapping AS (
    SELECT 
        cck.id as old_id,
        ctk.id as new_id
    FROM claude_code_key cck
    JOIN common_code_key ctk ON 
        ctk.type = 'ClaudeCode' AND
        ctk.access_token = cck.access_token AND
        ctk.created_at = cck.created_at
)
INSERT INTO claude_code_token_refresh_info (
    common_key_id,
    refresh_token,
    expires_at,
    client_id,
    email,
    claude_account_type,
    last_refresh_time,
    created_at,
    updated_at
)
SELECT 
    km.new_id as common_key_id,
    cck.refresh_token,
    cck.expires_at,
    cck.client_id,
    cck.email,
    cck.type as claude_account_type,
    NULL as last_refresh_time,
    cck.created_at,
    cck.updated_at
FROM claude_code_key cck
JOIN key_mapping km ON cck.id = km.old_id;

-- 3. 添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_common_code_key_type ON common_code_key(type);
CREATE INDEX IF NOT EXISTS idx_common_code_key_status ON common_code_key(status);
CREATE INDEX IF NOT EXISTS idx_common_code_key_type_status ON common_code_key(type, status);
CREATE INDEX IF NOT EXISTS idx_claude_code_token_refresh_info_common_key_id ON claude_code_token_refresh_info(common_key_id);
CREATE INDEX IF NOT EXISTS idx_claude_code_token_refresh_info_expires_at ON claude_code_token_refresh_info(expires_at);

-- 4. 记录迁移结果
DO $$
DECLARE
    claude_code_count INTEGER;
    common_token_count INTEGER;
    refresh_info_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO claude_code_count FROM claude_code_key;
    SELECT COUNT(*) INTO common_token_count FROM common_code_key WHERE type = 'ClaudeCode';
    SELECT COUNT(*) INTO refresh_info_count FROM claude_code_token_refresh_info;
    
    RAISE NOTICE 'Migration completed: % claude_code_key records migrated to % common_code_key records and % refresh_info records', 
        claude_code_count, common_token_count, refresh_info_count;
END $$;

-- 5. 备份原表（可选，建议保留一段时间后再删除）
-- ALTER TABLE claude_code_key RENAME TO claude_code_key_backup_v2;

-- 提示：在确认迁移成功且新系统运行正常后，可以执行以下语句删除旧表
-- DROP TABLE IF EXISTS claude_code_key_backup_v2;