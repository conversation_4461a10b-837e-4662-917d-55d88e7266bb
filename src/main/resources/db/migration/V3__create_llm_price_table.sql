-- 创建LLM价格表
CREATE TABLE IF NOT EXISTS sp_console_system_llm_price (
    id                        BIGSERIAL PRIMARY KEY,
    audio_cache_prompt        NUMERIC(29, 18),
    audio_completion          NUMERIC(29, 18),
    audio_prompt              NUMERIC(29, 18),
    create_time               BIGINT,
    image_cache_prompt        NUMERIC(29, 18),
    image_completion          NUMERIC(29, 18),
    image_prompt              NUMERIC(29, 18),
    reasoning_completion      NUMERIC(29, 18),
    statuses                  INTEGER,
    system_model_id           BIGINT,
    text_cache_prompt         NUMERIC(29, 18),
    text_cache_prompt_write1h NUMERIC(29, 18),
    text_cache_prompt_write5m NUMERIC(29, 18),
    text_completion           NUMERIC(29, 18),
    text_prompt               NUMERIC(29, 18),
    type                      INTEGER
);

-- 创建索引
CREATE INDEX idx_llm_price_system_model_id ON sp_console_system_llm_price(system_model_id);
CREATE INDEX idx_llm_price_type ON sp_console_system_llm_price(type);
CREATE INDEX idx_llm_price_statuses ON sp_console_system_llm_price(statuses);

-- 添加外键约束
ALTER TABLE sp_console_system_llm_price 
    ADD CONSTRAINT fk_llm_price_system_model 
    FOREIGN KEY (system_model_id) 
    REFERENCES sp_console_system_model(id) 
    ON DELETE CASCADE;