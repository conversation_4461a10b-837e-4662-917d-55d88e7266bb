<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds">
    <!-- 定义环境变量 -->
    <property name="LOG_PATH" value="${HOME}/logs/kmg"/>
    <property name="LOG_ARCHIVE" value="${LOG_PATH}/archive"/>
    <property name="APP_NAME" value="kmg"/>

    <!-- 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>
                %d{yyyy-MM-dd HH:mm:ss} %highlight(%-5p) [%15.15t] [%X{trace_id}] [%X{span_id}] %cyan(%c{1}) : %m%n%ex
            </pattern>
        </encoder>
    </appender>

    <appender name="OTEL" class="io.opentelemetry.instrumentation.logback.v1_0.OpenTelemetryAppender">
        <appender-ref ref="STDOUT" />
        <appender-ref ref="ASYNC_FILE" />
    </appender>

    <!-- 文件滚动日志 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${APP_NAME}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_ARCHIVE}/${APP_NAME}-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>
                %d{yyyy-MM-dd HH:mm:ss} %-5p [%t] [%X{trace_id}] [%X{span_id}] %c{1} : %m%n%ex
            </pattern>
        </encoder>
    </appender>

    <!-- 异步日志（提升性能） -->
    <appender name="ASYNC_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <appender-ref ref="FILE"/>
    </appender>

    <!-- Root 日志级别 -->
    <root level="INFO">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="ASYNC_FILE"/>
<!--        <appender-ref ref="OTEL"/>-->
    </root>

    <!-- 开发环境增强日志 -->
    <springProfile name="dev">
        <root level="DEBUG">
            <appender-ref ref="OTEL" />
        </root>
        <logger name="org.springframework.web" level="DEBUG"/>
    </springProfile>

    <!-- 生产环境抑制无关日志 -->
    <springProfile name="prod">
        <logger name="org.springframework.boot.autoconfigure" level="WARN"/>
    </springProfile>
</configuration>